<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use App\Migrate\CheckInvoiceAP511;

class Ap511SyncBackgroundCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap511:sync-background {processId} {startDate} {endDate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run AP511 invoice synchronization in background';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $processId = $this->argument('processId');
        $startDate = $this->argument('startDate');
        $endDate = $this->argument('endDate');

        $this->info("Starting AP511 sync process: {$processId}");
        $this->info("Date range: {$startDate} to {$endDate}");

        try {
            // Load sync process data
            $syncPath = "sync_processes/{$processId}.json";
            if (!Storage::exists($syncPath)) {
                $this->error("Process file not found: {$syncPath}");
                return 1;
            }

            $syncData = json_decode(Storage::get($syncPath), true);
            
            // Initialize CheckInvoiceAP511 instance
            $checker = new CheckInvoiceAP511();
            
            // Calculate total days for progress tracking
            $startDateCarbon = Carbon::parse($startDate);
            $endDateCarbon = Carbon::parse($endDate);
            $totalDays = $startDateCarbon->diffInDays($endDateCarbon) + 1;
            
            $this->info("Total days to process: {$totalDays}");

            $currentDate = $startDateCarbon->copy();
            $processedRecords = 0;
            $errors = 0;
            $logEntries = [];

            while ($currentDate->lte($endDateCarbon)) {
                // Check if process should be stopped
                $currentSyncData = json_decode(Storage::get($syncPath), true);
                if ($currentSyncData['status'] === 'stopped') {
                    $this->info("Process stopped by user");
                    break;
                }

                $currentDateStr = $currentDate->format('Y-m-d');
                $this->info("Processing date: {$currentDateStr}");

                try {
                    // Add log entry
                    $logEntries[] = [
                        'timestamp' => Carbon::now()->format('Y-m-d H:i:s'),
                        'level' => 'INFO',
                        'message' => "Processing date: {$currentDateStr}"
                    ];

                    // Process invoices for this date using the existing logic
                    $recordsProcessed = $this->processInvoicesForDate($checker, $currentDate);
                    
                    $processedRecords += $recordsProcessed;
                    
                    $logEntries[] = [
                        'timestamp' => Carbon::now()->format('Y-m-d H:i:s'),
                        'level' => 'INFO',
                        'message' => "Processed {$recordsProcessed} records for {$currentDateStr}"
                    ];

                    $this->info("Processed {$recordsProcessed} records for {$currentDateStr}");

                } catch (\Exception $e) {
                    $errors++;
                    $errorMessage = "Error processing date {$currentDateStr}: " . $e->getMessage();
                    
                    $logEntries[] = [
                        'timestamp' => Carbon::now()->format('Y-m-d H:i:s'),
                        'level' => 'ERROR',
                        'message' => $errorMessage
                    ];
                    
                    $this->error($errorMessage);
                }

                // Update progress
                $processedDays = $startDateCarbon->diffInDays($currentDate) + 1;
                $progress = round(($processedDays / $totalDays) * 100, 2);

                $syncData['current_date'] = $currentDateStr;
                $syncData['processed_records'] = $processedRecords;
                $syncData['errors'] = $errors;
                $syncData['log'] = json_encode($logEntries);
                $syncData['progress'] = $progress;

                Storage::put($syncPath, json_encode($syncData));

                $currentDate->addDay();
                
                // Small delay to prevent overwhelming the system
                sleep(1);
            }

            // Mark as completed
            $syncData['status'] = 'completed';
            $syncData['completed_at'] = Carbon::now();
            $syncData['final_processed_records'] = $processedRecords;
            $syncData['final_errors'] = $errors;
            
            $logEntries[] = [
                'timestamp' => Carbon::now()->format('Y-m-d H:i:s'),
                'level' => 'INFO',
                'message' => "Synchronization completed. Total processed: {$processedRecords}, Errors: {$errors}"
            ];
            
            $syncData['log'] = json_encode($logEntries);
            Storage::put($syncPath, json_encode($syncData));

            $this->info("Sync process completed successfully");
            $this->info("Total processed: {$processedRecords}");
            $this->info("Total errors: {$errors}");

            return 0;

        } catch (\Exception $e) {
            $this->error("Fatal error in sync process: " . $e->getMessage());
            
            // Update sync data with error status
            if (isset($syncData)) {
                $syncData['status'] = 'failed';
                $syncData['error_message'] = $e->getMessage();
                $syncData['failed_at'] = Carbon::now();
                Storage::put($syncPath, json_encode($syncData));
            }
            
            return 1;
        }
    }

    /**
     * Process invoices for a specific date
     *
     * @param CheckInvoiceAP511 $checker
     * @param Carbon $date
     * @return int Number of records processed
     */
    private function processInvoicesForDate($checker, $date)
    {
        $dateStr = $date->format('Y-m-d');
        $recordsProcessed = 0;

        try {
            // Get fulfillment orders for this date
            $orders = DB::connection('oracle_ep')
                ->table('fl_fulfilment_order as fo')
                ->join('fl_workflow_status as fws', 'fo.status_id', '=', 'fws.status_id')
                ->join('pm_status_desc as psd', 'fws.status_id', '=', 'psd.status_id')
                ->leftJoin('fl_payment_advice as fpa', 'fo.fulfilment_order_id', '=', 'fpa.fulfilment_order_id')
                ->select([
                    'fo.fulfilment_order_id',
                    'fo.workflow_instance_id',
                    'fo.invoice_no',
                    'fo.payment_advice_no',
                    'fo.status_id',
                    'fo.created_date',
                    'fo.poco_no',
                    'fo.requisition_id',
                    'psd.status_desc',
                    'fpa.payment_advice_no as pa_no_from_advice'
                ])
                ->whereDate('fo.created_date', $dateStr)
                ->whereIn('fo.status_id', [41030, 41530])
                ->get();

            foreach ($orders as $order) {
                // Check if record already exists
                $existingRecord = DB::connection('mysql_ep_support')
                    ->table('ep_invoice_check')
                    ->where('poco_no', $order->poco_no)
                    ->where('inv_no', $order->invoice_no)
                    ->first();

                if ($existingRecord) {
                    continue; // Skip if already processed
                }

                // Process the invoice using the existing logic
                $invoiceData = [
                    'poco_no' => $order->poco_no,
                    'pa_no' => $order->payment_advice_no ?: $order->pa_no_from_advice,
                    'inv_no' => $order->invoice_no,
                    'fl_order_id' => $order->fulfilment_order_id,
                    'fl_req_id' => $order->requisition_id,
                    'status_name' => $order->status_desc,
                    'status_id' => $order->status_id,
                    'inv_date_created' => $order->created_date,
                    'payment_advice_no' => $order->payment_advice_no,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ];

                // Check if invoice exists in AP511 (simplified check)
                $invoiceData['is_ap511'] = $this->checkInvoiceInAP511($order->invoice_no);
                
                // Check if PA number exists
                $invoiceData['is_pa_no_exist'] = !empty($invoiceData['pa_no']) ? 1 : 0;

                // Get additional payment info
                $paymentInfo = $this->getPaymentInfo($order->fulfilment_order_id);
                $invoiceData['payment_reference_no'] = $paymentInfo['payment_reference_no'];
                $invoiceData['payment_date'] = $paymentInfo['payment_date'];
                $invoiceData['file_name'] = $paymentInfo['file_name'];

                // Insert into database
                DB::connection('mysql_ep_support')
                    ->table('ep_invoice_check')
                    ->insert($invoiceData);

                $recordsProcessed++;
            }

        } catch (\Exception $e) {
            $this->error("Error processing date {$dateStr}: " . $e->getMessage());
            throw $e;
        }

        return $recordsProcessed;
    }

    /**
     * Check if invoice exists in AP511 files
     *
     * @param string $invoiceNo
     * @return int 1 if exists, 0 if not
     */
    private function checkInvoiceInAP511($invoiceNo)
    {
        if (empty($invoiceNo)) {
            return 0;
        }

        // This is a simplified check - in real implementation you would
        // search through OSB files or other AP511 data sources
        try {
            // Check in OSB files or other AP511 data sources
            // For now, we'll do a simple pattern check
            $ap511Files = DB::connection('mysql_ep_support')
                ->table('ep_invoice_detail')
                ->where('invoice_no', $invoiceNo)
                ->where('file_name', 'like', '%AP511%')
                ->exists();

            return $ap511Files ? 1 : 0;

        } catch (\Exception $e) {
            $this->error("Error checking AP511 for invoice {$invoiceNo}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get payment information for an order
     *
     * @param string $fulfilmentOrderId
     * @return array
     */
    private function getPaymentInfo($fulfilmentOrderId)
    {
        $paymentInfo = [
            'payment_reference_no' => null,
            'payment_date' => null,
            'file_name' => null
        ];

        try {
            $payment = DB::connection('oracle_ep')
                ->table('fl_payment_advice')
                ->where('fulfilment_order_id', $fulfilmentOrderId)
                ->first();

            if ($payment) {
                $paymentInfo['payment_reference_no'] = $payment->payment_reference_no;
                $paymentInfo['payment_date'] = $payment->payment_date;
                
                // Get file name from tracking diary
                $tracking = DB::connection('oracle_ep')
                    ->table('pm_tracking_diary')
                    ->where('workflow_instance_id', $payment->workflow_instance_id)
                    ->orderBy('created_date', 'desc')
                    ->first();
                
                if ($tracking && $tracking->file_name) {
                    $paymentInfo['file_name'] = $tracking->file_name;
                }
            }

        } catch (\Exception $e) {
            $this->error("Error getting payment info for order {$fulfilmentOrderId}: " . $e->getMessage());
        }

        return $paymentInfo;
    }
}