<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class Ap511InvoiceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the AP511 Invoice Check dashboard
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function dashboard(Request $request)
    {
        $year = $request->get('year', Carbon::now()->year);
        $month = $request->get('month'); // Default to null (All Months)

        // Get summary statistics
        $statistics = $this->getSummaryStatistics($year, $month);

        // Get available years for filter dropdown
        $availableYears = $this->getAvailableYears();

        return view('ap511_check_invoice.dashboard', compact('statistics', 'availableYears', 'year', 'month'));
    }

    /**
     * Display the invoice list with DataTable
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function invoiceList(Request $request)
    {
        $year = $request->get('year', Carbon::now()->year);
        $month = $request->get('month'); // Default to null (All Months)

        // Get available years for filter dropdown
        $availableYears = $this->getAvailableYears();

        return view('ap511_check_invoice.invoice_list', compact('availableYears', 'year', 'month'));
    }

    /**
     * Get summary statistics for dashboard
     *
     * @param int $year
     * @param int|null $month
     * @return array
     */
    private function getSummaryStatistics($year, $month = null)
    {
        $query = "SELECT 
            YEAR(inv_date_created) AS 'YEAR',
            MONTH(inv_date_created) AS 'MONTH',
            is_ap511 AS 'IS_AP511',
            COUNT(id) AS 'TOTAL'
        FROM ep_support.ep_invoice_check 
        WHERE YEAR(inv_date_created) = ?";
        
        $params = [$year];
        
        if ($month) {
            $query .= " AND MONTH(inv_date_created) = ?";
            $params[] = $month;
        }
        
        $query .= " GROUP BY 1, 2, 3 ORDER BY 1, 2, 3";

        $results = DB::connection('mysql_ep_support')->select($query, $params);
        
        // Process results into a more usable format
        $processed = [
            'total_invoices' => 0,
            'ap511_invoices' => 0,
            'non_ap511_invoices' => 0,
            'monthly_breakdown' => []
        ];

        foreach ($results as $row) {
            $monthKey = $row->MONTH;
            $isAp511 = $row->IS_AP511;
            $total = $row->TOTAL;

            if (!isset($processed['monthly_breakdown'][$monthKey])) {
                $processed['monthly_breakdown'][$monthKey] = [
                    'month' => $monthKey,
                    'month_name' => Carbon::create()->month($monthKey)->format('F'),
                    'total' => 0,
                    'ap511' => 0,
                    'non_ap511' => 0
                ];
            }

            $processed['monthly_breakdown'][$monthKey]['total'] += $total;
            $processed['total_invoices'] += $total;

            if ($isAp511 == 1) {
                $processed['monthly_breakdown'][$monthKey]['ap511'] += $total;
                $processed['ap511_invoices'] += $total;
            } else {
                $processed['monthly_breakdown'][$monthKey]['non_ap511'] += $total;
                $processed['non_ap511_invoices'] += $total;
            }
        }

        return $processed;
    }

    /**
     * Get available years from the database
     *
     * @return array
     */
    private function getAvailableYears()
    {
        $query = "SELECT DISTINCT YEAR(inv_date_created) as year 
                  FROM ep_support.ep_invoice_check 
                  WHERE inv_date_created IS NOT NULL 
                  ORDER BY year DESC";
        
        $results = DB::connection('mysql_ep_support')->select($query);
        
        return array_column($results, 'year');
    }

    /**
     * AJAX endpoint for DataTables
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoiceData(Request $request)
    {
        // Clean and validate input
        $year = $request->get('year');
        $month = $request->get('month');
        $start = intval($request->get('start', 0));
        $length = intval($request->get('length', 10));
        $searchValue = '';
        
        // Handle search parameter safely
        $search = $request->get('search');
        if (is_array($search) && isset($search['value'])) {
            $searchValue = $search['value'];
        }
        
        // Clean year and month parameters (handle array or HTML entities)
        if (is_array($year)) {
            $year = $year[0] ?? null;
        }
        if (is_array($month)) {
            $month = $month[0] ?? null;
        }
        
        // Decode HTML entities and clean
        $year = html_entity_decode($year);
        $month = html_entity_decode($month);
        
        // Remove any quotes or brackets
        $year = trim($year, '"[]');
        $month = trim($month, '"[]');
        
        // Validate year and month
        if ($year && (!is_numeric($year) || $year < 2000 || $year > 2050)) {
            $year = null;
        }
        if ($month && (!is_numeric($month) || $month < 1 || $month > 12)) {
            $month = null;
        }

        // Base query for counting total records
        $baseQuery = DB::connection('mysql_ep_support')->table('ep_invoice_check');
        
        // Get total count (all records)
        $totalRecords = $baseQuery->count();

        // Build filtered query
        $query = DB::connection('mysql_ep_support')
            ->table('ep_invoice_check')
            ->select([
                'id',
                'poco_no',
                'pa_no',
                'is_pa_no_exist',
                'inv_no',
                'fl_order_id',
                'fl_req_id',
                'status_name',
                'status_id',
                'is_ap511',
                'file_name',
                'payment_reference_no',
                'inv_date_created',
                'payment_date',
                'cancelation_date',
                'payment_advice_no',
                'created_at'
            ]);

        // Apply year filter
        if ($year) {
            $query->whereYear('inv_date_created', $year);
        }

        // Apply month filter
        if ($month) {
            $query->whereMonth('inv_date_created', $month);
        }

        // Apply search filter
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('poco_no', 'like', "%{$searchValue}%")
                  ->orWhere('pa_no', 'like', "%{$searchValue}%")
                  ->orWhere('inv_no', 'like', "%{$searchValue}%")
                  ->orWhere('file_name', 'like', "%{$searchValue}%")
                  ->orWhere('payment_reference_no', 'like', "%{$searchValue}%")
                  ->orWhere('status_name', 'like', "%{$searchValue}%");
            });
        }

        // Get filtered count (before pagination)
        $filteredRecords = $query->count();

        // Apply pagination
        $results = $query->orderBy('id', 'desc')
            ->skip($start)
            ->take($length)
            ->get();

        // Format the data
        $data = [];
        foreach ($results as $row) {
            // Safe date formatting function
            $formatDate = function($date) {
                if (!$date || $date === '0000-00-00 00:00:00') {
                    return '-';
                }
                try {
                    return Carbon::parse($date)->format('d-M-Y H:i:s');
                } catch (\Exception $e) {
                    return '-';
                }
            };

            $data[] = [
                'id' => $row->id,
                'poco_no' => $row->poco_no ?: '-',
                'pa_no' => $row->pa_no ?: '-',
                'is_pa_no_exist_text' => $row->is_pa_no_exist == 1 ? 
                    '<span class="badge badge-success">Yes</span>' : 
                    '<span class="badge badge-danger">No</span>',
                'inv_no' => $row->inv_no ?: '-',
                'fl_order_id' => $row->fl_order_id ?: '-',
                'fl_req_id' => $row->fl_req_id ?: '-',
                'status_name' => $row->status_name ?: '-',
                'is_ap511_text' => $row->is_ap511 == 1 ? 
                    '<span class="badge badge-success">Yes</span>' : 
                    '<span class="badge badge-danger">No</span>',
                'file_name' => $row->file_name ?: '-',
                'payment_reference_no' => $row->payment_reference_no ?: '-',
                'inv_date_created' => $formatDate($row->inv_date_created),
                'payment_date' => $formatDate($row->payment_date),
                'payment_advice_no' => $row->payment_advice_no ?: '-'
            ];
        }

        return response()->json([
            'draw' => intval($request->get('draw')),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data,
            'debug' => [
                'year' => $year,
                'month' => $month,
                'search' => $searchValue,
                'start' => $start,
                'length' => $length
            ]
        ]);
    }

    /**
     * Export invoice data to Excel using Python script
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportExcel(Request $request)
    {
        $year = $request->get('year');
        $month = $request->get('month');
        
        // Generate filename based on current date if no filters provided
        $currentDate = Carbon::now();
        if ($year && $month) {
            $filename = 'InvoicePendingPaymentNotInAP511_' . $year . str_pad($month, 2, '0', STR_PAD_LEFT) . '.xlsx';
        } else {
            $filename = 'InvoicePendingPaymentNotInAP511_' . $currentDate->format('Ym') . '.xlsx';
        }

        // Get the data using the same source as invoice list
        $query = DB::connection('mysql_ep_support')
            ->table('ep_invoice_check')
            ->select([
                'id',
                'poco_no',
                'pa_no',
                'is_pa_no_exist',
                'inv_no',
                'fl_order_id',
                'fl_req_id',
                'status_name',
                'status_id',
                'is_ap511',
                'file_name',
                'payment_reference_no',
                'inv_date_created',
                'payment_date',
                'cancelation_date',
                'payment_advice_no',
                'created_at'
            ]);

        // Apply filters
        if ($year) {
            $query->whereYear('inv_date_created', $year);
        }
        if ($month) {
            $query->whereMonth('inv_date_created', $month);
        }

        // Only get non-AP511 invoices (pending payment not in AP511)
        $query->where('is_ap511', 0);

        $results = $query->orderBy('id', 'desc')->get();

        // Convert results to array format for Python script
        $data = [];
        foreach ($results as $row) {
            $data[] = [
                'inv_no' => $row->inv_no ?? '',
                'poco_no' => $row->poco_no ?? '',
                'pa_no' => $row->pa_no ?? ''
            ];
        }

        // Create temporary JSON file
        $jsonFile = storage_path('app/temp/export_data_' . uniqid() . '.json');
        $outputFile = storage_path('app/temp/' . $filename);
        
        // Ensure temp directory exists
        if (!is_dir(storage_path('app/temp'))) {
            mkdir(storage_path('app/temp'), 0755, true);
        }

        // Write JSON data to file
        file_put_contents($jsonFile, json_encode($data, JSON_UNESCAPED_UNICODE));

        // Execute Python script
        $scriptPath = base_path('appscripts/generate_excel.py');
        $command = "python \"$scriptPath\" \"$jsonFile\" \"$outputFile\"";
        
        // Execute command and capture output
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);

        // Clean up JSON file
        if (file_exists($jsonFile)) {
            unlink($jsonFile);
        }

        if ($returnCode !== 0) {
            return response()->json([
                'error' => 'Failed to generate Excel file',
                'details' => implode('\n', $output)
            ], 500);
        }

        // Check if Excel file was created
        if (!file_exists($outputFile)) {
            return response()->json([
                'error' => 'Excel file was not generated',
                'details' => 'The Python script completed but no output file was found'
            ], 500);
        }

        // Return file for download
        return response()->download($outputFile, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ])->deleteFileAfterSend(true);
    }

    /**
     * Display the sync interface
     *
     * @return \Illuminate\View\View
     */
    public function sync()
    {
        return view('ap511_check_invoice.sync');
    }

    /**
     * Start the synchronization process
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function startSync(Request $request)
    {
        try {
            $filterType = $request->get('filterType');
            $processId = uniqid('sync_', true);
            
            // Validate input
            if ($filterType === 'year') {
                $year = $request->get('year');
                if (!$year || !is_numeric($year)) {
                    return response()->json(['success' => false, 'message' => 'Invalid year specified']);
                }
                $startDate = Carbon::create($year, 1, 1)->format('Y-m-d');
                $endDate = Carbon::create($year, 12, 31)->format('Y-m-d');
            } else {
                $startDate = $request->get('startDate');
                $endDate = $request->get('endDate');
                
                if (!$startDate || !$endDate) {
                    return response()->json(['success' => false, 'message' => 'Start date and end date are required']);
                }
                
                if (Carbon::parse($startDate)->gt(Carbon::parse($endDate))) {
                    return response()->json(['success' => false, 'message' => 'Start date cannot be after end date']);
                }
            }

            // Delete existing records for the date range
            $this->deleteExistingRecords($startDate, $endDate);

            // Create sync process record
            $syncData = [
                'process_id' => $processId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'running',
                'progress' => 0,
                'current_date' => $startDate,
                'processed_records' => 0,
                'total_records' => 0,
                'errors' => 0,
                'started_at' => Carbon::now(),
                'log' => json_encode([])
            ];

            // Store sync process data in a file (simulating database storage)
            Storage::put("sync_processes/{$processId}.json", json_encode($syncData));

            // Start background process (in a real application, you'd use a queue)
            $this->startBackgroundSync($processId, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'processId' => $processId,
                'message' => 'Synchronization started successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Sync start error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to start synchronization']);
        }
    }

    /**
     * Stop the synchronization process
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stopSync(Request $request)
    {
        try {
            $processId = $request->get('processId');
            
            if (!$processId) {
                return response()->json(['success' => false, 'message' => 'Process ID is required']);
            }

            // Update sync process status
            $syncPath = "sync_processes/{$processId}.json";
            
            if (Storage::exists($syncPath)) {
                $syncData = json_decode(Storage::get($syncPath), true);
                $syncData['status'] = 'stopped';
                $syncData['stopped_at'] = Carbon::now();
                
                Storage::put($syncPath, json_encode($syncData));
                
                return response()->json(['success' => true, 'message' => 'Synchronization stopped']);
            }

            return response()->json(['success' => false, 'message' => 'Process not found']);

        } catch (\Exception $e) {
            Log::error('Sync stop error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to stop synchronization']);
        }
    }

    /**
     * Get synchronization progress
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSyncProgress(Request $request)
    {
        try {
            $processId = $request->get('processId');
            
            if (!$processId) {
                return response()->json(['success' => false, 'message' => 'Process ID is required']);
            }

            $syncPath = "sync_processes/{$processId}.json";
            
            if (!Storage::exists($syncPath)) {
                return response()->json(['success' => false, 'message' => 'Process not found']);
            }

            $syncData = json_decode(Storage::get($syncPath), true);
            
            // Calculate progress
            $totalDays = Carbon::parse($syncData['start_date'])->diffInDays(Carbon::parse($syncData['end_date'])) + 1;
            $processedDays = Carbon::parse($syncData['start_date'])->diffInDays(Carbon::parse($syncData['current_date'])) + 1;
            $percentage = $totalDays > 0 ? round(($processedDays / $totalDays) * 100, 2) : 0;

            // Calculate estimated time remaining
            $startTime = Carbon::parse($syncData['started_at']);
            $currentTime = Carbon::now();
            $elapsedTime = $startTime->diffInSeconds($currentTime);
            $estimatedTotal = $percentage > 0 ? ($elapsedTime / $percentage) * 100 : 0;
            $estimatedRemaining = max(0, $estimatedTotal - $elapsedTime);
            
            $estimatedTimeFormatted = $this->formatTime($estimatedRemaining);

            $progress = [
                'percentage' => $percentage,
                'currentStatus' => $syncData['status'],
                'currentDate' => $syncData['current_date'],
                'processedRecords' => $syncData['processed_records'],
                'estimatedTime' => $estimatedTimeFormatted,
                'completed' => $syncData['status'] === 'completed' || $syncData['status'] === 'stopped'
            ];

            if ($progress['completed']) {
                $progress['results'] = [
                    'totalProcessed' => $syncData['processed_records'],
                    'totalErrors' => $syncData['errors'],
                    'totalTime' => $this->formatTime($elapsedTime),
                    'logContent' => $this->formatLogContent(json_decode($syncData['log'], true))
                ];
            }

            return response()->json(['success' => true, 'progress' => $progress]);

        } catch (\Exception $e) {
            Log::error('Sync progress error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to get synchronization progress']);
        }
    }

    /**
     * Download synchronization log
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function downloadSyncLog(Request $request)
    {
        try {
            $processId = $request->get('processId');
            
            if (!$processId) {
                return response('Process ID is required', 400);
            }

            $syncPath = "sync_processes/{$processId}.json";
            
            if (!Storage::exists($syncPath)) {
                return response('Process not found', 404);
            }

            $syncData = json_decode(Storage::get($syncPath), true);
            $logEntries = json_decode($syncData['log'], true);
            
            $logContent = "AP511 Invoice Synchronization Log\n";
            $logContent .= "Process ID: {$processId}\n";
            $logContent .= "Start Date: {$syncData['start_date']}\n";
            $logContent .= "End Date: {$syncData['end_date']}\n";
            $logContent .= "Started At: {$syncData['started_at']}\n";
            $logContent .= "Status: {$syncData['status']}\n";
            $logContent .= "Processed Records: {$syncData['processed_records']}\n";
            $logContent .= "Errors: {$syncData['errors']}\n";
            $logContent .= str_repeat('-', 80) . "\n\n";
            
            foreach ($logEntries as $entry) {
                $logContent .= "[{$entry['timestamp']}] {$entry['level']}: {$entry['message']}\n";
            }

            return response($logContent, 200, [
                'Content-Type' => 'text/plain',
                'Content-Disposition' => 'attachment; filename="sync_log_' . $processId . '.txt"'
            ]);

        } catch (\Exception $e) {
            Log::error('Sync log download error: ' . $e->getMessage());
            return response('Failed to download log', 500);
        }
    }

    /**
     * Delete existing records for the specified date range
     *
     * @param string $startDate
     * @param string $endDate
     * @return void
     */
    private function deleteExistingRecords($startDate, $endDate)
    {
        DB::connection('mysql_ep_support')
            ->table('ep_invoice_check')
            ->whereDate('inv_date_created', '>=', $startDate)
            ->whereDate('inv_date_created', '<=', $endDate)
            ->delete();
    }

    /**
     * Start background synchronization process
     *
     * @param string $processId
     * @param string $startDate
     * @param string $endDate
     * @return void
     */
    private function startBackgroundSync($processId, $startDate, $endDate)
    {
        // In a real application, you would dispatch a queue job here
        // For this demo, we'll simulate the process with a simple background task
        
        // Create a command to run the sync process
        $command = "php artisan ap511:sync-background {$processId} {$startDate} {$endDate}";
        
        // Execute the command in background (platform-specific)
        if (PHP_OS_FAMILY === 'Windows') {
            exec("start /B {$command}", $output, $return_var);
        } else {
            exec("{$command} > /dev/null 2>&1 &", $output, $return_var);
        }
    }

    /**
     * Format time in seconds to human readable format
     *
     * @param int $seconds
     * @return string
     */
    private function formatTime($seconds)
    {
        if ($seconds < 60) {
            return round($seconds) . 's';
        } elseif ($seconds < 3600) {
            return round($seconds / 60) . 'm';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = round(($seconds % 3600) / 60);
            return $hours . 'h ' . $minutes . 'm';
        }
    }

    /**
     * Format log content for display
     *
     * @param array $logEntries
     * @return string
     */
    private function formatLogContent($logEntries)
    {
        $formatted = '';
        
        foreach ($logEntries as $entry) {
            $levelClass = strtolower($entry['level']);
            $formatted .= "<div class=\"log-entry {$levelClass}\">";
            $formatted .= "<span class=\"log-timestamp\">[{$entry['timestamp']}]</span> ";
            $formatted .= "<span class=\"log-level\">{$entry['level']}:</span> ";
            $formatted .= "<span class=\"log-message\">{$entry['message']}</span>";
            $formatted .= "</div>";
        }
        
        return $formatted;
    }
}