<?php $__env->startSection('cssprivate'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php if(Auth::user()): ?>
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="fa fa-dashboard"></i>AP511 Invoice Dashboard<br>
                <small>Track and monitor AP511 invoice processing</small>
            </h1>
        </div>
    </div>
    
    <!-- Modern Navigation Tabs -->
    <div class="modern-tabs-container">
        <div class="row">
            <div class="col-lg-12">
                <div class="modern-tabs">
                    <a href="<?php echo e(url('/ap511-check-invoice/dashboard')); ?>" class="modern-tab active">
                        <div class="tab-icon">
                            <i class="fa fa-dashboard"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Dashboard</h4>
                            <span>Overview & Analytics</span>
                        </div>
                    </a>
                    <a href="<?php echo e(url('/ap511-check-invoice/list')); ?>" class="modern-tab">
                        <div class="tab-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Invoice List</h4>
                            <span>Browse Records</span>
                        </div>
                    </a>
                    <a href="<?php echo e(url('/ap511-check-invoice/sync')); ?>" class="modern-tab">
                        <div class="tab-icon">
                            <i class="fa fa-refresh"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Sync Process</h4>
                            <span>Run Synchronization</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- <div class="row">
        <div class="col-lg-12 text-right">
            <div class="timestamp-badge">
                <i class="fa fa-clock-o"></i> <?php echo e(Carbon\Carbon::now()->format('d-M-Y H:i:s')); ?>

            </div>
        </div>
    </div> -->

    <!-- Modern Filter Section -->
    <div class="modern-filter-section">
        <div class="filter-card">
            <div class="filter-header">
                <h3><i class="fa fa-filter"></i> Filter Options</h3>
            </div>
            <form id="form-filter" action="<?php echo e(url('/ap511-check-invoice/dashboard')); ?>" method="get" class="modern-form">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="filterYear">Year <span class="required">*</span></label>
                        <div class="select-wrapper">
                            <select id="filterYear" name="year" class="modern-select" required>
                                <?php $__currentLoopData = $availableYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $availableYear): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($availableYear); ?>" <?php echo e($availableYear == $year ? 'selected' : ''); ?>>
                                        <?php echo e($availableYear); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <i class="fa fa-chevron-down select-arrow"></i>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label for="filterMonth">Month</label>
                        <div class="select-wrapper">
                            <select id="filterMonth" name="month" class="modern-select">
                                <option value="">All Months</option>
                                <?php for($i = 1; $i <= 12; $i++): ?>
                                    <option value="<?php echo e($i); ?>" <?php echo e($i == $month ? 'selected' : ''); ?>>
                                        <?php echo e(Carbon\Carbon::create()->month($i)->format('F')); ?>

                                    </option>
                                <?php endfor; ?>
                            </select>
                            <i class="fa fa-chevron-down select-arrow"></i>
                        </div>
                    </div>
                    <div class="filter-action">
                        <button type="button" id="applyFilter" class="modern-btn primary">
                            <i class="fa fa-search"></i>
                            <span>Apply Filter</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Modern Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card purple">
            <div class="stat-content">
                <div class="stat-icon">
                    <i class="fa fa-file-text-o"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" id="totalInvoices"><?php echo e(number_format($statistics['total_invoices'])); ?></div>
                    <div class="stat-label">Total Invoices</div>
                </div>
            </div>
            <div class="stat-decoration"></div>
        </div>

        <div class="stat-card green">
            <div class="stat-content">
                <div class="stat-icon">
                    <i class="fa fa-check-circle"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" id="ap511Invoices"><?php echo e(number_format($statistics['ap511_invoices'])); ?></div>
                    <div class="stat-label">AP511 Invoices</div>
                </div>
            </div>
            <div class="stat-decoration"></div>
        </div>

        <div class="stat-card blue">
            <div class="stat-content">
                <div class="stat-icon">
                    <i class="fa fa-times-circle"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" id="nonAp511Invoices"><?php echo e(number_format($statistics['non_ap511_invoices'])); ?></div>
                    <div class="stat-label">Non-AP511 Invoices</div>
                </div>
            </div>
            <div class="stat-decoration"></div>
        </div>

        <div class="stat-card orange">
            <div class="stat-content">
                <div class="stat-icon">
                    <i class="fa fa-percent"></i>
                </div>
                <div class="stat-info">
                    <div class="stat-number" id="ap511Percentage">
                        <?php echo e($statistics['total_invoices'] > 0 ? number_format(($statistics['ap511_invoices'] / $statistics['total_invoices']) * 100, 1) : 0); ?>%
                    </div>
                    <div class="stat-label">AP511 Percentage</div>
                </div>
            </div>
            <div class="stat-decoration"></div>
        </div>
    </div>

    <!-- Modern Table Section -->
    <div class="modern-table-section">
        <div class="table-card">
            <div class="table-header">
                <h3><i class="fa fa-bar-chart"></i> Monthly Breakdown</h3>
                <div class="table-actions">
                    <div class="search-info">
                        <span id="breakdown-info"><?php echo e(count($statistics['monthly_breakdown'])); ?> months</span>
                    </div>
                </div>
            </div>
            <div class="table-content">
                <div class="table-responsive">
                    <table id="monthlyBreakdownTable" class="modern-table">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th>Total Invoices</th>
                                <th>AP511 Invoices</th>
                                <th>Non-AP511 Invoices</th>
                                <th>AP511 %</th>
                            </tr>
                        </thead>
                        <tbody id="monthlyBreakdownTableBody">
                            <?php 
                                $sortedBreakdown = collect($statistics['monthly_breakdown'])->sortBy('month')->values()->all();
                             ?>
                            <?php $__currentLoopData = $sortedBreakdown; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $monthData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><span class="month-name"><?php echo e($monthData['month_name']); ?></span></td>
                                <td><span class="badge total"><?php echo e(number_format($monthData['total'])); ?></span></td>
                                <td><span class="badge success"><?php echo e(number_format($monthData['ap511'])); ?></span></td>
                                <td><span class="badge danger"><?php echo e(number_format($monthData['non_ap511'])); ?></span></td>
                                <td>
                                    <span class="percentage <?php echo e($monthData['total'] > 0 && ($monthData['ap511'] / $monthData['total']) * 100 > 50 ? 'good' : 'warning'); ?>">
                                        <?php echo e($monthData['total'] > 0 ? number_format(($monthData['ap511'] / $monthData['total']) * 100, 1) : 0); ?>%
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if(count($statistics['monthly_breakdown']) == 0): ?>
                            <tr>
                                <td colspan="5" class="no-data">
                                    <i class="fa fa-info-circle"></i> No data available for the selected period
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<style>
:root {
    --primary-color: #4f46e5;
    --primary-light: #818cf8;
    --primary-dark: #3730a3;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --green-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --blue-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --orange-gradient: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%);
    --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --background-overlay: rgba(255, 255, 255, 0.95);
}

/* Reset and remove old styles */
.statistics-card,
.widget,
.widget-extra,
.themed-background-dark,
.nav-pills,
.block,
.block-title,
.block-content {
    all: unset;
}

/* Modern Navigation Tabs */
.modern-tabs-container {
    margin: 30px 0;
    padding: 0 20px;
}

.modern-tabs {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.modern-tab {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    text-decoration: none;
    color: var(--secondary-color);
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.modern-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: var(--transition);
}

.modern-tab.active::before,
.modern-tab:hover::before {
    transform: scaleX(1);
}

.modern-tab:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
    color: var(--primary-color);
}

.modern-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--card-shadow-hover);
}

.tab-icon {
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.modern-tab.active .tab-icon {
    background: rgba(255, 255, 255, 0.2);
}

.tab-content h4 {
    margin: 0 0 5px 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.tab-content span {
    font-size: 1.125rem;
    opacity: 0.8;
}

/* Timestamp Badge */
.timestamp-badge {
    background: var(--background-overlay);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 1.125rem;
    color: var(--secondary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Filter Section */
.modern-filter-section {
    margin: 30px 0;
    padding: 0 20px;
}

.filter-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
}

.filter-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.filter-row {
    display: flex;
    align-items: end;
    gap: 30px;
    padding: 30px;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 1.125rem;
}

.required {
    color: var(--danger-color);
}

.select-wrapper {
    position: relative;
}

.modern-select {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 1.125rem;
    transition: var(--transition);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.modern-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.select-arrow {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    pointer-events: none;
}

.filter-action {
    flex-shrink: 0;
}

.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.125rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.modern-btn.primary {
    background: var(--primary-color);
    color: white;
}

.modern-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

/* Modern Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin: 30px 20px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 32px;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 160px;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--card-shadow-hover);
}

.stat-content {
    display: flex;
    align-items: center;
    gap: 24px;
    position: relative;
    z-index: 2;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-card.purple .stat-icon {
    background: var(--purple-gradient);
}

.stat-card.green .stat-icon {
    background: var(--green-gradient);
}

.stat-card.blue .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.orange .stat-icon {
    background: var(--orange-gradient);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
    color: #1a202c;
}

.stat-label {
    font-size: 1.125rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.stat-decoration {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    opacity: 0.1;
}

.stat-card.purple .stat-decoration {
    background: var(--purple-gradient);
}

.stat-card.green .stat-decoration {
    background: var(--green-gradient);
}

.stat-card.blue .stat-decoration {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.orange .stat-decoration {
    background: var(--orange-gradient);
}

/* Modern Table Section */
.modern-table-section {
    margin: 30px 20px;
}

.table-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 24px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.table-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.search-info {
    font-size: 1.125rem;
    color: var(--secondary-color);
    padding: 8px 12px;
    background: rgba(79, 70, 229, 0.1);
    border-radius: 6px;
}

.table-content {
    padding: 0;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table thead th {
    background: #f8fafc;
    padding: 20px 24px;
    text-align: left;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 2px solid #e2e8f0;
    font-size: 1.125rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table tbody td {
    padding: 20px 24px;
    border-bottom: 1px solid #f1f5f9;
    color: #374151;
    font-size: 1.125rem;
}

.modern-table tbody tr:hover {
    background: #f8fafc;
}

.month-name {
    font-weight: 600;
    color: var(--primary-color);
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.badge.total {
    background: #e0e7ff;
    color: #3730a3;
}

.badge.success {
    background: #d1fae5;
    color: #047857;
}

.badge.danger {
    background: #fee2e2;
    color: #dc2626;
}

.percentage {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
}

.percentage.good {
    background: #d1fae5;
    color: #047857;
}

.percentage.warning {
    background: #fef3c7;
    color: #92400e;
}

.no-data {
    text-align: center;
    color: var(--secondary-color);
    font-style: italic;
    padding: 40px !important;
}

/* DataTables Custom Styling */
.dataTables_wrapper {
    padding: 20px 30px;
}

.dataTables_length select,
.dataTables_filter input {
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 1.125rem;
    transition: var(--transition);
}

.dataTables_length select:focus,
.dataTables_filter input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.dataTables_paginate .paginate_button {
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.dataTables_paginate .paginate_button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.dataTables_paginate .paginate_button.current {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.dataTables_info {
    color: var(--secondary-color);
    font-size: 1.125rem;
}

/* Loading state */
.dataTables_processing {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    color: var(--primary-color);
    font-weight: 600;
}

/* Table scroll indicator */
.table-responsive {
    position: relative;
}

.table-responsive::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.table-responsive.scrollable::after {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-tabs {
        flex-direction: column;
        align-items: center;
    }

    .modern-tab {
        min-width: 100%;
        max-width: 400px;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-action {
        align-self: stretch;
    }

    .modern-btn {
        width: 100%;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        margin: 20px 10px;
    }

    .stat-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .modern-table-section {
        margin: 20px 10px;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .dataTables_wrapper {
        padding: 15px 20px;
    }

    .dataTables_length,
    .dataTables_filter,
    .dataTables_info,
    .dataTables_paginate {
        text-align: center;
        margin: 10px 0;
    }
}

/* Loading Animation */
@keyframes  pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Smooth Transitions for Dynamic Content */
.stat-number {
    transition: all 0.3s ease;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

/* Focus States for Accessibility */
.modern-select:focus,
.modern-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.modern-tab:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 4px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTables with modern styling following invoice_list pattern
    $('#monthlyBreakdownTable').DataTable({
        pageLength: 12,
        lengthMenu: [[12, 24, 48, -1], [12, 24, 48, 'All']],
        ordering: false, // Disable ordering
        dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-sm-6"i><"col-sm-6"p>>',
        language: {
            processing: '<div style="color: var(--primary-color); font-weight: 600;"><i class="fa fa-spinner fa-spin"></i> Loading monthly breakdown...</div>',
            search: "Search:",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            },
            emptyTable: "No monthly breakdown data available",
            zeroRecords: "No matching records found"
        },
        responsive: true,
        scrollX: true,
        autoWidth: false,
        drawCallback: function(settings) {
            // Check if table is scrollable
            var table = $('#monthlyBreakdownTable');
            var wrapper = table.closest('.table-responsive');
            if (table.width() > wrapper.width()) {
                wrapper.addClass('scrollable');
            } else {
                wrapper.removeClass('scrollable');
            }
        }
    });

    // Apply filter with loading animation
    $('#applyFilter').click(function(e) {
        e.preventDefault();
        
        var year = $('#filterYear').val();
        var month = $('#filterMonth').val();
        
        // Add loading state
        $(this).addClass('loading').prop('disabled', true);
        $('.stat-number').addClass('loading');
        
        // Show loading overlay
        $('body').append('<div id="loading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.3); z-index: 9999; display: flex; align-items: center; justify-content: center;"><div style="background: white; padding: 30px; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.15); text-align: center;"><i class="fa fa-spinner fa-spin fa-2x" style="color: var(--primary-color); margin-bottom: 16px;"></i><div style="color: #374151; font-weight: 600;">Loading data...</div></div></div>');
        
        // Make AJAX request
        $.ajax({
            url: '<?php echo e(url("/ap511-check-invoice/dashboard-data")); ?>',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                year: year,
                month: month
            },
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    
                    // Update summary cards with animation
                    $('#totalInvoices').text(numberWithCommas(data.total_invoices));
                    $('#ap511Invoices').text(numberWithCommas(data.ap511_invoices));
                    $('#nonAp511Invoices').text(numberWithCommas(data.non_ap511_invoices));
                    
                    var percentage = data.total_invoices > 0 ? (data.ap511_invoices / data.total_invoices * 100) : 0;
                    $('#ap511Percentage').text(percentage.toFixed(1) + '%');
                    
                    // Update monthly breakdown table
                    var table = $('#monthlyBreakdownTable').DataTable();
                    table.clear();

                    if (Object.keys(data.monthly_breakdown).length === 0) {
                        table.row.add([
                            '<span class="no-data"><i class="fa fa-info-circle"></i> No data available for the selected period</span>',
                            '', '', '', ''
                        ]).draw();
                    } else {
                        // Sort monthly breakdown by month number
                        var sortedBreakdown = Object.keys(data.monthly_breakdown).sort(function(a, b) {
                            return data.monthly_breakdown[a].month - data.monthly_breakdown[b].month;
                        });

                        $.each(sortedBreakdown, function(index, key) {
                            var monthData = data.monthly_breakdown[key];
                            var monthPercentage = monthData.total > 0 ? (monthData.ap511 / monthData.total * 100) : 0;
                            var percentageClass = monthPercentage > 50 ? 'good' : 'warning';

                            table.row.add([
                                '<span class="month-name">' + monthData.month_name + '</span>',
                                '<span class="badge total">' + numberWithCommas(monthData.total) + '</span>',
                                '<span class="badge success">' + numberWithCommas(monthData.ap511) + '</span>',
                                '<span class="badge danger">' + numberWithCommas(monthData.non_ap511) + '</span>',
                                '<span class="percentage ' + percentageClass + '">' + monthPercentage.toFixed(1) + '%</span>'
                            ]);
                        });
                        table.draw();
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                alert('Error loading data. Please try again.');
            },
            complete: function() {
                $('#loading').remove();
                $('#applyFilter').removeClass('loading').prop('disabled', false);
                $('.stat-number').removeClass('loading');
            }
        });
    });
    
    // Helper function to format numbers with commas
    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    // Add smooth scroll for better UX
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>