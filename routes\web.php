<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of the routes that are handled
| by your application. Just tell <PERSON><PERSON> the URIs it should respond
| to using a Closure or controller method. Build something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});


Auth::routes();
Route::get('/logout', 'Auth\LoginController@logout');
Route::get('/home', 'HomeController@index');
Route::match(array('GET', 'POST'), 'payment/response-callback', 'Auth\LoginController@responseCallback');
Route::get('/case-stat-report', function () {
    return redirect('http://192.168.120.207/case-stat-report');
    //return view('welcome');
});
Route::get('/case-stat-report-users', function () {
    return redirect('http://192.168.120.207/case-stat-report-users');
    //return view('welcome');
});


/** This for webservice eAduan. eAduan will call this api to sync data **/
Route::get('/rest/user/{loginID}', 'BatchController@syncUser');
Route::get('/rest/supplier/user/{loginID}', 'BatchController@syncSupplierUser');
Route::get('/rest/organization/user/{loginID}', 'BatchController@syncOrganizationUser');



/** This for search eP **/

// Search Info SM Modules
Route::get('/find/icno/',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@detailsUser',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/icno/{icno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getDetailsUser',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/mofno/',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@detailsUserByMof',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/mofno/{mofno}',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getDetailsUserByMof',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/epno/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@detailsUserByEpNo',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/epno/{epno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getDetailsUserByEpNo',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/appl-id/{appl_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getHistoryAppl',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/sap/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@detailsUserBySapVendorCode',
    'roles' => Config::get('constant.roles_access_smmodule'),
]);
Route::get('/find/sap/{sapvendercode}',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getDetailsUserBySapVendorCode',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osblog/{softcertreqid}/{servicecode}',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getSoftcertLogDetail',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/success-signing/{userId}/{type}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getLatestSuccessSigning',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/pending-transaction/{supplierID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getDetailsPendingTransaction',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/items-supplier/{supplierID}',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@findDetailsSupplierItems',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/list-transaction/{supplierId}/{year}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getListTransactionSupplier',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/byname/',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getDetailsUserByName',
    'roles' =>  array_merge(Config::get('constant.roles_access_smmodule'), Config::get('constant.roles_codi_ep')),
]);
Route::get('/find/list-transaction/{supplierId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@historyApplId',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/list-appl-history/cs/',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpSupplierController@getDetailsUserByName',
    'roles' => Config::get('constant.roles_access_smmodule'),
]);

Route::get('/find/supplier', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpSupplierController@findSupplier',
    'roles' => Config::get('constant.roles_access_smmodule'),

]);
Route::match(['get','post'],'/find/ssmno/',[
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@detailsUserBySsm',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::match(['get','post'], '/find/ssmno/{ssm_no}/{business_type}',[
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@detailsUserBySsm',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/download/mofcert/{mofCert}', function ($mofCert) {
    //dd($mofCert);
    //$remotePath =  "/docuRepo/prd/ngep/2018/SM/VIRTUAL_CERT_NEW/2018-02-23/374161_20180223_174440.pdf";
    $data = DB::connection('oracle_nextgen_rpt')->table('SM_MOF_CERT')->where('mof_cert_id', $mofCert)
        ->first();
    if ($data) {
        if ($data->file_path != null) {
            $remotePath =  "/docuRepo/prd/ngep/" . $data->file_path . "/" . $data->file_name;
            //dd($remotePath);
            $contents = SSH::into('portal')->getString($remotePath);
            return response()->attachmentPdf($contents);
        }
    }
    return "Supplier must login to eP and download first!";
});

Route::get('/download/attachment/cancel-reject/{attId}', function ($attId) {
    $data = DB::connection('oracle_nextgen_rpt')->table('sm_attachment')->where('attachment_id', $attId)->first();
    if ($data) {
        if ($data->file_path != null) {
            $remotePath =  "/docuRepo/prd/ngep/" . $data->file_path . "/" . $data->file_name;
            $contents = SSH::into('portal')->getString($remotePath);
            return response()->attachmentPdf($contents);
        }
    }
    return "Document not found!";
});
// END Info SM Modules


/** Roles for Patch Data **/
Route::get('/find/userpersonnel/{applId}/{personnelId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@searchUserPersonnelDetails',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::post('/find/userpersonnel/{applId}/{personnelId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@updateUserPersonnelDetails',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::get('/find/userpersonnel-sync-role/{personnelId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@syncRoleSMPersonnelUser',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::get('/resend/softcert/request', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@resendSoftcertRequest',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::get('/resend/softcert/request-certificate', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@resendSoftcertRequestCertificate',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::get('/resend/softcert/request-tg-to-dg', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@resendSoftcertRequestTgtoDg',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::get('/find/user-sync-role/{loginId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@syncRoleUserEp',
    'roles' => Config::get('constant.roles_patch_ep'),
]);

Route::get('/find/supplier/address/{personnelId}', [
    'middleware' => ['auth'],
    'uses' => 'EpSupplierController@getListTotalAddress',
]);

Route::get('/find/supplier/application-inquiry/count/{appId}', [
    'middleware' => ['auth'],
    'uses' => 'EpSupplierController@getCountApplicationInquiries'
]);

Route::get('/find/supplier/application-inquiry/list/{appId}', [
    'middleware' => ['auth'],
    'uses' => 'EpSupplierController@getListApplicationInquiries'
]);

Route::get('/find/supplier/application-rejected/count/{appId}', [
    'middleware' => ['auth'],
    'uses' => 'EpSupplierController@getCountApplicationRejected'
]);

Route::get('/find/supplier/application-rejected/list/{appId}', [
    'middleware' => ['auth'],
    'uses' => 'EpSupplierController@getListApplicationRejected'
]);

Route::get('/find/supplier/{applId}/{supplierId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@searchSupplierDetails',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::post('/find/supplier/{applId}/{supplierId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@updateSupplierDetails',
    'roles' => Config::get('constant.roles_patch_ep'),
]);

Route::get('/find/supplier/ssm', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@findSsmCompany',
    'roles' => Config::get('constant.roles_patch_ep'),
]);

/**  Patching eP Table : Only allow for technical prod-support*/
Route::get('/find/patch-ep', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@patchTableEp',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::post('/find/patch-ep', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@updatePatchTableEp',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::post('/find/ep/table', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@getDetailRecordByTable',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::post('/find/ep/table/del', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@deleteDetailRecordByTable',
    'roles' => Config::get('constant.roles_patch_ep'),
]);




Route::get('/find/sm/site-visit/revert', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@revertSiteVisitToApproval',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
/** END Roles for Patch Data **/



Route::get('/find/uom/', 'EpController@listUom');
Route::get('/find/uom/{uom}', 'EpController@getListUom');
Route::get('/uom/download/searching/{uom}', 'EpController@downloadSearchingUom');
Route::get('/uom/download/all/{uom}', 'EpController@downloadAllUom');

Route::get('/find/item/', 'ItemController@searchListItems');
Route::get('/find/item/{search}', 'ItemController@searchListItems');
Route::get('/find/items/unspsc', 'ItemController@searchListUNSPSCItems');
Route::get('/find/items/unspsc/{search}', 'ItemController@searchListUNSPSCItems');
Route::get('/find/item/unspsc/brand/{search}', 'ItemController@searchListItemBrandByUNSPSCID');
Route::get('/find/item/unspsc/type/{search}', 'ItemController@searchListItemTypeByUNSPSCID');
Route::get('/find/item/unspsc/measurement/{search}', 'ItemController@searchListItemMeasurementByUNSPSCID');
Route::get('/find/item/unspsc/color/', 'ItemController@searchListItemColor');
Route::get('/find/items/supplier', 'ItemController@searchListProductSupplierPendingCodification');
Route::get('/find/items/supplier/{search}', 'ItemController@searchListProductSupplierPendingCodification');

Route::match(['get', 'post', 'put'], '/find/items/codi-task', 'ItemController@searchListProductSupplierCodificationTask');
Route::match(['get', 'post', 'put'], '/find/items/codi-task/{search}', 'ItemController@searchListProductSupplierCodificationTask');
Route::get('/find/codi/supplier', 'EpSupplierController@findSupplierCodi');

Route::get('/find/contract/', 'ContractController@findContract');
Route::get('/find/contract/detail/{contract_id}', 'ContractController@eKontrakDetail');

Route::get('/find/pplan/', 'PplanController@findPPlan');
Route::get('/find/pplan/docno/workflow/{docNo}', 'PplanController@findPPlanListWorkFlowStatusDocument');

Route::get('/find/contract/committee', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ContractController@searchCTCommitteMembersByContractNo',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/contractInfo/{contractNo}/{contractVersion}', 'ContractController@contractItems');
Route::get('/find/contractInfo/{contractNo}', 'ContractController@contractItemsLatestVersion');
Route::post('/find/contractInfo/{contractNo}/{contractVersion}', 'ActionEpController@updateContractItems');
Route::get('/find/ct-summary', 'ContractController@contractInfoPage');
Route::get('/ct-summary/download/item/{ver}/{docno}', 'ContractController@downloadItem');
Route::get('/find/contract/ver/{verid}', 'ContractController@contractVer');
Route::get('/find/address/agency/{agencyid}/{ver}', 'ContractController@agencyId');


Route::get('/find/identity', 'EpController@getIdentity');
Route::get('/find/userlogin', 'WebServiceEpController@getUserLogin');
Route::get('/find/userlogin/gpki-check', 'WebServiceEpController@checkGpkiLastSigning');


Route::get('/find/orgcode/{orgcode}', 'EpOrganizationController@searchByOrgCode');
Route::get('/find/orgcode/', 'EpOrganizationController@searchByOrgCodeDefault');
Route::get('/find/org/icno/{icno}', 'EpOrganizationController@searchByIdentificationNo');
Route::get('/find/org/icno/', 'EpOrganizationController@searchByIdentificationNoDefault');
Route::get('/find/orgcode/address/delivery/{orgprofileid}', 'EpOrganizationController@searchDeliveryAddressByOrgProfileId');
Route::get('/find/orgcode/address/receiving/{orgprofileid}/{icno}', 'EpOrganizationController@searchReceivingAddress');
Route::get('/find/orgcode/usergroup/{orgprofileid}', 'EpOrganizationController@searchUserGroupOrg');




//Route::get('/support/task', 'EpSupportController@listEpTask');
Route::match(['get', 'post', 'put'], '/support/task', 'EpSupportController@listEpTask');
Route::get('/support/task/save', 'EpSupportController@saveTask');
Route::get('/support/task/detail/{taskID}', 'EpSupportController@getTask');
Route::get('/support/task/list/{carian}', 'EpSupportController@searchEpTask');
Route::post('/support/task/list', 'EpSupportController@searchEpTask2');
Route::get('/crm/case/{caseno}', 'EpSupportController@getDetailCaseCRM');  // json
//Route::get('/support/crm/case/', 'CRMController@detailCaseCRM');
Route::get('/support/crm/case', 'CRMController@detailCaseCRM');
Route::post('/support/crm/case/update/{caseId}', 'CRMController@updateCaseCRM');
Route::post('/support/crm/case/assignpic/{userid}', 'CRMController@assignPicForTask');
Route::post('/support/crm/case/updateResolution/{caseId}', 'CRMController@updateResolution');
Route::post('/support/crm/case/delete-note-files/{caseId}', 'CRMController@deleteNoteFiles');

Route::match(['get', 'post', 'put'], '/support/task-missing', 'EpSupportController@listEpTaskMissing');
Route::get('/support/task-missing/detail/{taskID}', 'EpSupportController@getTaskMissing');
Route::get('/support/task-missing/list/{carian}', 'EpSupportController@searchInputEpTaskMissing');
Route::get('/support/task-missing/list/', 'EpSupportController@searchInputEpTaskMissing');
Route::post('/support/task-missing/list', 'EpSupportController@searchEpTaskMissing');
Route::get('/support/task-missing/check/{caseno}', 'EpSupportController@getCRMCaseAndCheckTaskMissing');
Route::get('/support/task-missing/download', 'EpSupportController@downloadTaskMissing');
Route::get('/support/task-missing/upload', 'EpSupportController@uploadReport');
Route::match(['get', 'post'], '/support/task-missing/upload', 'EpSupportController@uploadReport');


Route::get('/payment/history', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getPaymentHistory',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/report/payment', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'RevenueReportController@showPaymentStatRoute',
    'roles' => array_merge(Config::get('constant.users_dev_ep'), Config::get('constant.users_report')),
]);
Route::get('/report/payment/{year}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'RevenueReportController@showPaymentStat',
    'roles' => array_merge(Config::get('constant.users_dev_ep'), Config::get('constant.users_report')),
]);
Route::get('/report/revenue/daily', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'RevenueReportController@showRevenueDailySummary',
    'roles' => array_merge(Config::get('constant.users_dev_ep'), Config::get('constant.users_report')),
]);
Route::get('/report/revenue/daily/request', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'RevenueReportController@getDataHtmlRevenueDailySummary',
    'roles' => array_merge(Config::get('constant.users_dev_ep'), Config::get('constant.users_report')),
]);
Route::get('/report/revenue/pending-transaction', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'RevenueReportController@showPendingTransactionStatistic',
    'roles' => array_merge(Config::get('constant.users_dev_ep'), Config::get('constant.users_report')),
]);
Route::get('/report/revenue/daily-stat-transaction', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'RevenueReportController@showStatPVTRDailyToday',
    'roles' => array_merge(Config::get('constant.users_dev_ep'), Config::get('constant.users_report')),
]);

//For user login summary report
Route::get('/report/loginSummaryReport', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'AppSchedulerController@showLoginStatisticReport',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

//For user login summary report search
Route::post('/report/loginSummaryReport/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'AppSchedulerController@showLoginStatisticReport',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

//For eP Support Statistic Action 
Route::get('/support/report/log/{type}/{date}', 'EpSupportController@getListActionLogHTML');

//For eP Support Statistic Action 
Route::get('/support/report/log-detail/{type}/{search}', 'EpSupportController@getListDetailInfoLogHTML');


Route::get('/find/gfmas/apive/{epNo}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@getApiveDetails',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/check/gfmas/apive/connection', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@checkConnectionGFMAS',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_it_support')),
]);
Route::get('/dashboard/gfmas/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardGfmas',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support')),
]);

//DASHBOARD
Route::group([
    'prefix' => 'dashboard/',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation'), Config::get('constant.roles_it_developer'))
], function () {
    //Dashboard/DashboardController
    Route::get('/main/', ['uses' => 'Dashboard\DashboardController@getDashboardMain']);
    Route::get('/batch/outbound', ['uses' => 'Dashboard\DashboardController@getDashboardBatchOutFolder']);
    Route::get('/userCreation', ['uses' => 'Dashboard\DashboardController@getDashboardUserCreation']);
    Route::get('/qtServiceRetry', ['uses' => 'Dashboard\DashboardController@checkQtServiceRetry']);
    Route::get('/listQtServiceRetry', ['uses' => 'Dashboard\DashboardController@listQtServiceRetry']);
    Route::get('/sqCloseTimeDaily', ['uses' => 'Dashboard\DashboardController@totalSqCloseTimeDaily']);
    Route::get('/checkMonitoringSoftcert', ['uses' => 'Dashboard\DashboardController@checkMonitoringSoftcert']);
    Route::get('/checkMonitoringSigningGpki', ['uses' => 'Dashboard\DashboardController@checkMonitoringSigningGpki']);
    Route::get('/checkQtSubmissionMonitoring', ['uses' => 'Dashboard\DashboardController@checkQtSubmissionMonitoring']);
    Route::get('/osbretry', ['uses' => 'Dashboard\DashboardController@checkOsbRetry']);
    Route::get('/osbnotifyretry', ['uses' => 'Dashboard\DashboardController@checkOsbNotifyRetry']);
    Route::get('/osbbatchretry', ['uses' => 'Dashboard\DashboardController@checkOsbBatchRetry']);
    Route::get('/monitorErrorEPScheduler', ['uses' => 'Dashboard\DashboardController@monitorErrorEPScheduler']);
    Route::get('/backlogmminf', ['uses' => 'Dashboard\DashboardController@backlogmminf']);
    Route::get('/processPayment', ['uses' => 'Dashboard\DashboardController@getDashboardProcessPayment']);
    Route::get('/processRemoveTask', ['uses' => 'Dashboard\DashboardController@getDashboardProcessRemoveTask']);
    Route::get('/listRemoveTask/{record_status}', 'Dashboard\DashboardController@getDetailedRemoveTask');
    Route::get('/pendingNotification', 'Dashboard\DashboardController@getPendingNotification');
    

    //Dashboard/BatchController
    Route::get('/batch/', ['uses' => 'Dashboard\BatchController@getDashboardBatch']);
    Route::get('/1gfmas/checkBatchFilePending', ['uses' => 'Dashboard\BatchController@checkBatchFilePending']);
    Route::get('/checkConnection', ['uses' => 'Dashboard\BatchController@checkConnectionGFMAS']);
    Route::get('/batch/aperr', ['uses' => 'Dashboard\DashboardController@getDashboardBatchAperr']);
    Route::get('/batch/aperr/list', ['uses' => 'Dashboard\DashboardController@getDashboardBatchAperrList']);
    //Dashboard/IgfmasController
    Route::get('/igfmas/', ['uses' => 'Dashboard\IgfmasController@getDashboardIgfmas']);
    Route::get('/igfmas/checkMonitoringOutboundProcess/', ['uses' => 'Dashboard\IgfmasController@checkMonitoringOutboundProcess']);
    Route::get('/igfmas/checkMonitoringInboundProcess/', ['uses' => 'Dashboard\IgfmasController@checkMonitoringInboundProcess']);

    //Dashboard/PhisController
    Route::get('/phis/', ['uses' => 'Dashboard\PhisController@getDashboardPhis']);
    Route::get('/phis/checkMonitoringWsFailedSent/', ['uses' => 'Dashboard\PhisController@checkMonitoringPhisWsFailedSent']);
    Route::get('/phis/resendWebServicePHIS/{serviceCode}/{docNo}', ['uses' => 'Dashboard\PhisController@resendWebServicePHIS']);



    //Dashboard/PaymentReceiptController
    Route::get('/paymentreceipt/', ['uses' => 'Dashboard\PaymentReceiptController@getDashboardPaymentReceipt']);
    Route::get('/paymentreceipt/checkSyncPaymentReceiptEpRazer/', ['uses' => 'Dashboard\PaymentReceiptController@checkSyncPaymentReceiptEpRazer']);
    Route::get('/paymentreceipt/list-order-payment/{sourcePayment}/{paymentDate}', ['uses' => 'Dashboard\PaymentReceiptController@listOrderByDate']);
    Route::get('/paymentreceipt/failed-sync-payment/{paymentDate}', ['uses' => 'Dashboard\PaymentReceiptController@showFailedSyncOrderId']);
    Route::get('/paymentreceipt/failed-sync-ar502/{paymentDate}', ['uses' => 'Dashboard\PaymentReceiptController@showFailedSyncAr502']);
    Route::get('/paymentreceipt/download-all-receipts/{paymentDate}', ['uses' => 'Dashboard\PaymentReceiptController@downloadAllReceiptPaymentSR']);
    Route::get('/paymentreceipt/show-maybank-with-razer-failed/', ['uses' => 'Dashboard\PaymentReceiptController@showFailedRazerWithMaybankSuccess']);
    Route::get('/paymentreceipt/show-wrong-card-type/', ['uses' => 'Dashboard\PaymentReceiptController@showWrongCardTypeRazerWithMaybank']);

    Route::get('/paymentreceipt/summary-transaction-daily/{paymentDate}', ['uses' => 'Dashboard\PaymentReceiptController@summaryTransactionDaily']);


    //Dashboard/OsbController
    Route::get('/osb/', ['uses' => 'Dashboard\OsbController@getDashboardOsb']);
    Route::get('/igfmasIntegrationMonitoring', ['uses' => 'Dashboard\OsbController@igfmasIntegrationMonitoring']);
    Route::get('/igfmasIntegrationMonitoringListDetails', ['uses' => 'Dashboard\OsbController@igfmasIntegrationMonitoringListDetails']);
    Route::get('/monitorErrorResponseOSB', ['uses' => 'Dashboard\OsbController@monitorErrorResponseOSB']);
    Route::get('/displayRecordsOSBError', ['uses' => 'Dashboard\OsbController@displayRecordsOSBError']);
    Route::get('/checkFileErrorInbound', ['uses' => 'Dashboard\OsbController@checkFileErrorInbound']);
    Route::get('/checkMonitoringErrorCheckChargingReceived', ['uses' => 'Dashboard\OsbController@checkMonitoringErrorCheckChargingReceived']);
    Route::get('/checkMonitoringSSMIntegration', ['uses' => 'Dashboard\OsbController@checkMonitoringSSMIntegration']);
    Route::get('/checkMonitoringJPNIntegration', ['uses' => 'Dashboard\OsbController@checkMonitoringJPNIntegration']);
    Route::get('/igfmas/item/code', ['uses' => 'Dashboard\OsbController@getItemCodeCheckingIgfmas']);

    //Dashboard/CrmController
    Route::get('/gfmascrm/', ['uses' => 'Dashboard\CrmController@getDashboardCrm']);
    Route::get('/checkMonitoringIntegrationCRMeP', ['uses' => 'Dashboard\CrmController@checkMOnitoringIntegrationCRMeP']);

    //Dashboard/MyGpisController
    Route::get('/mygpis/', ['uses' => 'Dashboard\MyGpisController@getDashboardMyGpis']);
    Route::get('/checkMonitoringMyGPIS/', ['uses' => 'Dashboard\MyGpisController@checkMonitoringMyGPIS']);

    //Dashboard/EgpaController
    Route::get('/egpa/', ['uses' => 'Dashboard\EgpaController@getDashboardEgpa']);
    Route::get('/checkMonitoringEgpa/', ['uses' => 'Dashboard\EgpaController@checkMonitoringEgpa']);

    //Dashboard/EgpaController
    Route::get('/spki/', ['uses' => 'Dashboard\SpkiController@getDashboardSpki']);
    Route::get('/spki/signing', ['uses' => 'Dashboard\SpkiController@getSigningMonitor']);
    Route::get('/spki/displayRecordSpki', ['uses' => 'Dashboard\SpkiController@displayRecordSpki']);
    Route::get('/spki/displayNewSoftcert', ['uses' => 'Dashboard\SpkiController@displayNewSoftcert']);
    Route::get('/spki/displayMonitoringRequest', ['uses' => 'Dashboard\SpkiController@displayMonitoringRequest']);
    Route::get('/spki/displayMonitoringRSigningSPKI', ['uses' => 'Dashboard\SpkiController@displayMonitoringRSigningSPKI']);
    Route::get('/spki/monitoringValidity', ['uses' => 'Dashboard\SpkiController@displayMonitoringValidity']);
    Route::get('/spki/monitoringValidity/details/{date}/{provider}', ['uses' => 'Dashboard\SpkiController@displayMonitoringValidityDetails']);

    //Dashboard/BPM
    Route::get('/bpm', ['uses' => 'Dashboard\DashboardController@getDashboardBpm']);
    Route::get('/bpm/monitoringHealth', ['uses' => 'Dashboard\DashboardController@checkHealthBPM']);
    Route::get('/bpm/stl/{composite}', ['uses' => 'Dashboard\DashboardBpmController@getDashboardBpmStlRunning']);

    //Dashboard/CheckConnectivity
    Route::get('/check-connection', ['uses' => 'Dashboard\DashboardController@srvCheckPing']);
    Route::get('/check-connection-service', ['uses' => 'Dashboard\DashboardController@srvCheckConnectionService']);
    Route::get('/check-connection-all-ws', ['uses' => 'Dashboard\DashboardController@srvCheckConnectionIntegrationAllWebService']);
    Route::get('/check-connection-all-batch-server', ['uses' => 'Dashboard\DashboardController@srvCheckConnectionIntegrationAllBatchService']);
});

Route::get('/dashboard/quartz/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardQuartz',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support')),
]);
Route::get('/dashboard/apive/diinterfacelog', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardDiInterfaceLogApive',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support')),
]);
Route::get('/dashboard/listSqCloseTimeDaily', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@listSqCloseTimeDaily',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

/*
Route::get('/dashboard/checkQtMonitoring', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkQtMonitoring',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

*/
//Set Open to all Users Access.
Route::get('/dashboard/checkQtMonitoring', 'DashboardController@checkQtMonitoring');
Route::get('/dashboard/checkQtMonitoringPublished', 'DashboardController@getQTPublishedByDay');
Route::get('/dashboard/checkQtMonitoringClosing', 'DashboardController@getQTClosingByDay');
Route::get('/dashboard/checkQtMonitoringPublished/find/{date}', 'DashboardController@getQTPublishedByMinistry');
Route::get('/dashboard/checkQtMonitoringClosing/find/{date}', 'DashboardController@getQTClosingByMinistry');
Route::get('/dashboard/checkQtMonitoringClosingTooEarly', 'DashboardController@checkQtMonitoringAsCloseTooEarly');


Route::get('/dashboard/emailNotificationsMonitoring', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getEmailNotificationMonitoring',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support')),
]);
Route::get('/dashboard/sqNotClosingMonitoring', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getSqNotClosingMonitoring',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support')),
]);

/*
 * Stop first access this URL, cause slow query! Impact performace DB Report!
Route::get('/dashboard/apive/outbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardApiveOutbound',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/apove/inbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboardApoveInbound',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkejbosb', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkEjbOsb',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkwsvalidation', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkWsValidationException',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkWsItemCodeErrorInGFM100', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkWsItemCodeErrorInGFM100',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkWsItemCodeErrorInMMINF', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@checkWsItemCodeErrorInMMINF',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
*/


Route::get('/dashboard/1gfmas/outbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboard1GfmasOutbound',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/dashboard/1gfmas/inbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@getDashboard1GfmasInbound',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);


Route::get('/dashboard/listPendingUserCreation', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@listPendingUserCreation',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/dashboard/listStuckUserCreation', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@listStuckUserCreation',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/dashboard/listPendingProcessPayment', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@listPendingProcessPayment',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/dashboard/listStuckProcessPayment', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@listStuckProcessPayment',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/list/1gfmas/folder', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@fetchFromFolderDisplay',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/list/1gfmas/fetch', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@fetchFromFolderList',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::post('/fetch/1gfmas/1gfmas-out', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@fetch1GfmasOutFolder',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::post('/fetch/1gfmas/process/in-out-file', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@processInOutFolder1GFMAS',
    'roles' => Config::get('constant.roles_adv_ep'),
]);



Route::get('/find/1gfmas/ws/log/{name}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@listLogOSB',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

/* PHIS */
Route::get('/find/1gfmas/ws/{search}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchWsOSBLog',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/1gfmas/ws', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@wsOSBLog',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/phis/view/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@phisView',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/find/phis/search/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@PhisSearchOrderDetails',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/phis/stuck-bpm/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@searchListPHISStuck',
    'roles' => Config::get('constant.roles_adv_ep'),
]);



/** MMINF TRIGGER **/
Route::get('/trigger/gfmas/mminf/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@mminfTriggerView',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/trigger/gfmas/mminf/search/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchPreMminf',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/trigger/gfmas/mminf/update/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@mminfTrigger',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/trigger/gfmas/mminf/docno/update', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@mminfTriggerByDocNo',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/trigger/gfmas/mminf/quartz', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@displayDashboardMminfQuartz',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/trigger/gfmas/mminf/diinterfacelog', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@displayDashboardMminfDiInterface',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/gfmas/materialCode', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchListMaterialCode',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/gfmas/materialCode/{search}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchListMaterialCode',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

//MMINF IS_SENT Update (data patch)
Route::get('/find/gfmas/mminfid/{mminfId}/{materialCode}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchMminfDetails',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/find/gfmas/mminfid/{mminfId}/{materialCode}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ActionEpController@updateMminfDetails',
    'roles' => Config::get('constant.roles_patch_ep'),
]);

/** APIVE TRIGGER **/
Route::get('/trigger/gfmas/apive/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@apiveTriggerView',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/trigger/gfmas/apive/search/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchPreApive',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/trigger/gfmas/apive/update/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@apiveTrigger',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/trigger/gfmas/triggerBPMCallback/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@triggerBPMCallback',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/trigger/gfmas/triggerBPMCallback/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@triggerBPMCallback',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/gfmas/stuck-charging-status-bpm/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchStuckTaskChargingStatus',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/gfmas/stuck-phis-160/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchStuckPHIS160',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/gfmas/stuck-pending-prcr-review/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchStuckTaskPendingPRCRReview',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/gfmas/stuck-pending-payment-query/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchStuckTaskPaymentQueryFromIgfmas',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/gfmas/redundant-saporderno/{year}/{month}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@searchRedundantSapOrderNo',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/1gfmas/pending/batch/{type}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@listPendingBatchHTML',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/list/1gfmas/batch/{type}/{serviceCode}/{transDate}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@listBatchHTML',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/list/1gfmas/batch/pendingfiles/{processID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@listBatchFilesPendingHTML',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/phis/ws/{search}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@searchWsOSBLog',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/phis/ws', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@wsOSBLog',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

/* QT */
Route::group([
    'prefix' => 'qt/doc',
    'middleware' => ['auth', 'roles'],
    'roles' => Config::get('constant.roles_adv_ep')
], function () {
    Route::get('/proposal/list', 'QtController@getProposalNosByQtNo');
    Route::get('view', 'QtController@viewQtDocumentChecklist');
    Route::post('view', 'QtController@viewQtDocumentChecklist');
    Route::get('validate', 'QtController@validateDocumentExist');
    Route::get('extract', 'QtController@extractDocument');
    Route::get('zip', 'QtController@zipMultipleDocuments');
    Route::group([
        'prefix' => 'download',
        'middleware' => ['auth', 'roles'],
        'roles' => Config::get('constant.roles_adv_ep')
    ], function () {
        Route::get('single', 'QtController@downloadSingleQtDocument');
        Route::post('multiple', 'QtController@downloadMultipleQtDocument');
    });
});

Route::get('/qt/dashboard', 'QtController@dashboard');
Route::get('/find/qt/checkingBSV', 'QtController@getBSVdetails');

Route::get('/find/qt/accept-history', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQtAcceptHistoryList',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/qt/accept-history/export', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@exportAcceptHistory',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/qt/extend', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@extendQt',
    'roles' => Config::get('constant.users_dev_ep'),
]);
Route::get('/qt/extend/showQTCountByDate/{qtDate}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@showQTCountByDate',
    'roles' => Config::get('constant.users_dev_ep'),
]);
Route::post('/qt/extend/confirm', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@actionExtend',
    'roles' => Config::get('constant.users_dev_ep'),
]);
Route::get('/find/qt/info', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQuatationTenderInfo',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/qtno', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchPage',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/qtno/{qtno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getSupplierAttendanceByQtNo',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/list/qt/detail/{supplierid}/{qtno}/{bsvattendanceid}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQtDetails',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/proposal', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchProposalSupplierByQuotationTender',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/proposal/{qtno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchProposalSupplierByQuotationTender',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/committee', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchQTCommitteMembersByQuotationTender',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/committee/{qtno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchQTCommitteMembersByQuotationTender',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/lawatan', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchTaklimatOrLawatanTapakByQuotationTender',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/lawatan/{qtno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchTaklimatOrLawatanTapakByQuotationTender',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/stuck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@searchListStuctTaskQt',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/sm/verifydel', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@CheckingDeleteMOF',
    'roles' => Config::get('constant.roles_patch_ep'),
]);
Route::get('/find/qt/suppresp', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getSupplierRespond',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/qt/suppexp', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getSupplierExp',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/qt/summary', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQuatationTenderSummary',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/qt/stucksummary', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQuotationTenderStuckSummary',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/qt/verifysupp/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQuotationTenderVerifySupp',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/qt/findqtbysupp/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getQTsubmitproposalbyMOF',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::post('/qt/pembekal/submit/download', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@downloadReportSubmit',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::post('/qt/pembekal/lulus/download', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@downloadReportLulus',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::post('/qt/pembekal/gagal/download', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@downloadReportGagal',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/qt/loastatus', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'QtController@getLOALOIStatusforQT',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

/** OSB **/
Route::post('/find/osb/batch/file', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@searchBatchOSBLog',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/batch/file/{fileName}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@findOsbBatchByName',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/batch/file', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@batchOSBLog',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/log', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@OSBLog',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/log/{carian}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@searchOSBLog',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/detail/log', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@searchOSBLogDetail',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/detail-rquid/log', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@searchOSBLogDetailByRqUid',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/osb/decrypt/{filename}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@decryptFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/osb/decrypt/tojson/{filename}/{service_code}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@decryptFileAndConvertToJSON',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/osb/decrypt/pendingfile/{processid}/{filename}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@decryptDataPendingFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/osb/decrypt-to-file/{filename}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@decryptFileToFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/find/osb/get-file/{filename}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@contentToFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);
Route::match(['get', 'post', 'put'], '/find/osb/error', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@searchListErrorTransactionOSB',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::match(['post'], '/osb/file/transfer', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@transferFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::match(['get'], '/osb/file/upload', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@initiateUploadFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::match(['post'], '/osb/file/upload', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@uploadFile',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::match(['get'], '/osb/file/transfer-file/{sourcetarget}/{typefile}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@transferFile1GFMASAndEp',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::get('/osb/file/content/search', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@searchDocNoFileIGFMAS',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_developer'), Config::get('constant.roles_ep_operation')),
]);
Route::match(['get'], '/osb/file/process-data', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@callBackEPToProcessFileFolderIN',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::match(['post'], '/find/osb/totalbytarget/{serviceType}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@getTotalOsbByTarget',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::match(['post'], '/find/osb/totalbyservice/{serviceType}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@getTotalOsbByService',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist')),
]);
Route::match(['get'], '/triggerProcessFileOSB', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@triggerProcessFileOSB',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

/** 
 * Trigger OSB to pickup files in OUT Folder eP.
 * @return type
 */
Route::match(['get'], '/triggerOSBPickupFileOutFolder', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@triggerOSBPickupFileOutFolder',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::match(['get'], '/triggerOSBPickupEperunding', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@triggerOSBPickupEperunding',
    'roles' => Config::get('constant.roles_adv_ep'),
]);


Route::get('/list/osb/batch/retry/{serviceName}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@listOsbServiceRetryHTML',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/list/osb/batch/retry/trigger/{type}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@triggerOsbRetry',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/osb/retry/trigger/{createdDate}/{target}/{serviceName}/{limitRecord}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@handleOsbServiceRetry',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::post('/list/osb/batch/retry/trigger/{type}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GFMASController@triggerOsbRetry',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/osb/retry/transid/{transID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'OSBController@osbTransIDPayload',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_it_support'), Config::get('constant.roles_ep_operation')),
]);

Route::match(['get', 'post', 'put'], '/support/task', 'EpSupportController@listEpTask');


/** BPM **/
Route::get('/find/bpm/task/docno', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListTaskBpm',
]);
Route::get('/find/bpm/task/docno/{docno}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListTaskBpmByDocNo',
]);
Route::get('/find/bpm/instanceid', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListCompositeInstanceId',
]);
Route::get('/find/sm/stuck-task/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListSmStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
//Open API
Route::get('/find/sm/stuck-task/json', 'BatchController@getListSmStuckTaskByJson');

Route::get('/find/stuck-task-invalid-completed/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListStuckTaskInvalidCompleted',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListFlStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-initiate-do/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListFLStuckTaskInitiateDO',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-pending-invoice/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListFLStuckTaskPendingInvoice',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-pending-payment-match/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListFLStuckTaskPendingPaymentMatch',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-pending-approval/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListFlStuckTaskPendingApproval',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task/integration', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListIntegrationStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task/workflow', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-prcr-epp-013-y/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListPrStuckEpp013Y',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task/CANCEL-POCO', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListCancelPOCO',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-prcr-mm-501-del/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListPrStuckMM501Del',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-task-fl-pending-payment-epp017-Y', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListFLPendingPaymentEpp017Y',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/fl/stuck-yep-cf', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListStuckYEPCF',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/fl/duplicate-invoice', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListDuplicateInvoice',
    'roles' => Config::get('constant.roles_adv_ep'),
]);






Route::get('/find/dp/stuck-task/sq', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListDpSQStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/dp/stuck-task/rn', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListDpRNStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/dp/stuck-task/rn-trigger-order', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListRNStuckTaskTriggerOrder',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/dp/stuck-task/codification', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'BPMController@getListDpCodificationStuckTask',
    'roles' => Config::get('constant.roles_adv_ep'),
]);



/** BPM API **/
Route::get('/bpm/task/find', 'BpmApiController@searchListTaskBpm');
Route::post('/bpm/task/find', 'BpmApiController@searchListTaskBpm');
Route::get('/bpm/instance/find', 'BpmApiController@searchListTaskBpmByCompositeInstanceId');
Route::post('/bpm/instance/find', 'BpmApiController@searchListTaskBpmByCompositeInstanceId');
Route::get('/bpm/instance/terminate/{instanceId}', 'BpmApiController@terminateInstance');
Route::post('/bpm/instance/terminate/{instanceId}', 'BpmApiController@terminateInstance');
Route::get('/bpm/process/workflow/{processId}/{status}', 'BpmApiController@detailProcessWorkflow');
Route::post('/bpm/process/workflow/{processId}/{status}', 'BpmApiController@detailProcessWorkflow');
Route::get('/bpm/process/bpmn/{processId}/{status}', 'BpmApiController@detailProcessBpmn');
Route::post('/bpm/process/bpmn/{processId}/{status}', 'BpmApiController@detailProcessBpmn');
Route::get('/bpm/process/action/id/{id}/status/{status}', 'BpmApiController@suspendResumeWorkflowAction');
Route::post('/bpm/process/action/id/{id}/status/{status}', 'BpmApiController@suspendResumeWorkflowAction');
Route::get('/bpm/process/action/alterflow/processid/{processId}/status/{status}', 'BpmApiController@alterflowProcessBpmn');
Route::post('/bpm/process/action/alterflow/processid/{processId}/status/{status}', 'BpmApiController@alterflowProcessBpmn');
Route::get('/bpm/process/action/alterflow/processid/{processId}/source/{source}/target/{target}', 'BpmApiController@submitAlterflowProcessBpmn');
Route::post('/bpm/process/action/alterflow/processid/{processId}/source/{source}/target/{target}', 'BpmApiController@submitAlterflowProcessBpmn');
Route::get('/bpm/process/action/variable/processid/{processId}/status/{status}', 'BpmApiController@variableProcessBpmn');
Route::post('/bpm/process/action/variable/processid/{processId}/status/{status}', 'BpmApiController@variableProcessBpmn');
Route::get('/bpm/process/submit/variable/processid/{processId}', 'BpmApiController@submitVariableProcessBpmn');
Route::post('/bpm/process/submit/variable/processid/{processId}', 'BpmApiController@submitVariableProcessBpmn');
Route::post('/bpm/process/grouptask/{taskid}', 'BpmApiController@getDetailTask');

//bpm worklist manager
Route::get('/bpm/worklist/find', 'BpmApiWorklistController@searchWorklist');
Route::post('/bpm/worklist/find', 'BpmApiWorklistController@searchWorklist');
Route::post('/bpm/worklist/userid/{userid}', 'BpmApiWorklistController@searchWorklistAssignment');
Route::post('/bpm/worklist/taskid/{taskid}', 'BpmApiWorklistController@searchWorklistTaskDetail');
Route::post('/bpm/worklist/delegate/taskId/{taskid}', 'BpmApiWorklistController@delegateTaskAction');
Route::post('/bpm/worklist/action/taskId/{taskid}', 'BpmApiWorklistController@worklistTaskAction');
Route::post('/bpm/worklist/execute/taskid/{taskid}', 'BpmApiWorklistController@executeTaskAction');
Route::post('/bpm/worklist/list/initiate/{userid}', 'BpmApiWorklistController@listInitiate');
Route::post('/bpm/worklist/initiate/process/{userid}', 'BpmApiWorklistController@initiateProcessAction');

//bpm task
Route::get('/bpm/task/withdraw/id/{id}/status/{status}', 'BpmApiController@withdrawTaskAction');
Route::post('/bpm/task/withdraw/id/{id}/status/{status}', 'BpmApiController@withdrawTaskAction');
Route::get('/bpm/task/reassign/id/{id}/status/{status}', 'BpmApiController@reassignTaskAction');
Route::post('/bpm/task/reassign/id/{id}/status/{status}', 'BpmApiController@reassignTaskAction');
Route::get('/bpm/task/execute/taskid/{taskid}', 'BpmApiController@executeTaskAction');
Route::post('/bpm/task/execute/taskid/{taskid}', 'BpmApiController@executeTaskAction');

//bpm api error handler
Route::get('/bpm/errhandler/find', 'BpmApiErrorHandlerController@listErrorHandler');
Route::post('/bpm/errhandler/find', 'BpmApiErrorHandlerController@listErrorHandler');

//bpm api instance query
Route::get('/bpm/instance/query', 'BpmApiInstanceQueryController@searchInstanceQuery');
Route::post('/bpm/instance/query', 'BpmApiInstanceQueryController@searchInstanceQuery');
Route::post('/bpm/instance/query/page', 'BpmApiInstanceQueryController@searchNextInstanceQuery');

//bpm api service manager
Route::get('/bpm/service/manager', 'BpmApiServiceManagerController@searchServiceManager');
Route::post('/bpm/service/manager', 'BpmApiServiceManagerController@searchServiceManager');
Route::post('/bpm/service/manager/create/{process}', 'BpmApiServiceManagerController@createServiceManager');
Route::get('/bpm/service/manager/detail', 'BpmApiServiceManagerController@detailService');

Route::get('/ws/isvalidtoken', 'BpmApiController@isValidToken');
Route::get('/bpm/sm/task/find', 'BpmApiController@searchSMTaskApplication');
Route::post('/bpm/sm/task/find', 'BpmApiController@doActionSMTaskApplication');

Route::get('/bpm/order/task/PurchaseRequest', 'BpmApiController@searchTaskPR');
Route::post('/bpm/order/task/PurchaseRequest', 'BpmApiController@doActionTaskPR');
Route::get('/bpm/order/task/ContractRequest', 'BpmApiController@searchTaskCR');
Route::post('/bpm/order/task/ContractRequest', 'BpmApiController@doActionTaskCR');

Route::get('/bpm/fl/task/doFulfilment', 'BpmApiController@searchTaskDO');
Route::post('/bpm/fl/task/doFulfilment', 'BpmApiController@doActionTaskDO');

Route::get('/bpm/fl/task/createSubmitInvoicePA', 'BpmApiController@searchTaskCreateSubmitInvoicePA');
Route::post('/bpm/fl/task/createSubmitInvoicePA', 'BpmApiController@doActionTaskCreateSubmitInvoicePA');
Route::get('/bpm/fl/task/modifyCancelInvoice', 'BpmApiController@searchTaskModifyCancelInvoice');
Route::post('/bpm/fl/task/modifyCancelInvoice', 'BpmApiController@doActionTaskModifyCancelInvoice');
Route::get('/bpm/fl/task/modifyCancelDO', 'BpmApiController@searchTaskModifyCancelDO');
Route::post('/bpm/fl/task/modifyCancelDO', 'BpmApiController@doActionTaskModifyCancelDO');
Route::get('/bpm/fl/task/stopInstruction', 'BpmApiController@searchTaskStopInstruction');
Route::post('/bpm/fl/task/stopInstruction', 'BpmApiController@doActionTaskStopInstruction');

//BPM API > Refire > Sourcing DP
Route::get('/bpm/sourcing/task/sqcreation', 'BpmApiController@searchTaskSourcingSQCreation');
Route::post('/bpm/sourcing/task/sqcreation', 'BpmApiController@doActionTaskSourcingSQCreation');
Route::get('/bpm/directpurchase/task/requestnote', 'BpmApiController@searchTaskDirectPurchaseRNCreation');
Route::post('/bpm/directpurchase/task/requestnote', 'BpmApiController@doActionDirectPurchaseRNCreation');

//BPM API > Refire > Sourcing QT
Route::get('/bpm/sourcing/task/qtcreation', 'BpmApiSourcingController@searchTaskSourcingQTCreation');
Route::post('/bpm/sourcing/task/qtcreation', 'BpmApiSourcingController@searchTaskSourcingQTCreation');
Route::get('/bpm/sourcing/task/evaluation', 'BpmApiSourcingController@searchTaskSourcingQTEvaluation');
Route::post('/bpm/sourcing/task/evaluation', 'BpmApiSourcingController@searchTaskSourcingQTEvaluation');
Route::get('/bpm/sourcing/task/finalization', 'BpmApiSourcingController@supplierFinalization');
Route::post('/bpm/sourcing/task/finalization', 'BpmApiSourcingController@supplierFinalization');
Route::get('/bpm/sourcing/task/refireEvaluation', 'BpmApiSourcingController@refireEvaluation');
Route::post('/bpm/sourcing/task/refireEvaluation', 'BpmApiSourcingController@refireEvaluation');
Route::get('/bpm/sourcing/task/ajaxActionEvaluation', 'BpmApiSourcingController@ajaxActionEvaluation');
Route::get('/bpm/sourcing/task/ajaxCheckInstanceID', 'BpmApiSourcingController@ajaxCheckInstanceID');

/** FL **/
Route::get('/find/trans/docno/{docNo}', 'FulfilmentController@findTransactionDocNo');
Route::get('/find/trans/docno/workflow/{docNo}', 'FulfilmentController@findListWorkFlowStatusDocument');
Route::get('/find/trans/track/docno/{docNo}', 'FulfilmentController@getListDocNoTracking');
Route::get('/find/trans/track/docno', 'FulfilmentController@getListDocNoTrackingDefault');
Route::get('/find/trans/track/docno/dofn/{docNo}', 'FulfilmentController@findListWorkFlowStatusDOFN');
Route::get('/find/trans/track/docno/yepmenu/{docNo}', 'FulfilmentController@findListYepMenuTasklistPoco');
Route::get('/find/trans/track/user/', 'FulfilmentController@getListDocNoTrackingByUser');
Route::post('/find/trans/track/user/', 'FulfilmentController@getListDocNoTrackingByUser');

/** Application Scheduler **/
Route::get('/find/app-scheduler/SIT', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'AppSchedulerController@getSIT',
]);

Route::get('/find/app-scheduler/Prod', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'AppSchedulerController@getProd',
]);

Route::get('/find/app-scheduler/PDS', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'AppSchedulerController@getPDS',
]);

/*
Route::get('/find/invoice/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'InvoiceController@findInvoice',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
*/
//Set open all 
Route::get('/find/contract/agreement/', 'PayloadGeneratorController@contractAgreement');
Route::get('/find/contract/agreement/draft/', 'PayloadGeneratorController@contractAgreementDraft');
Route::get('/find/contract/factoring/', 'PayloadGeneratorController@findFactoringCompany');
Route::get('/find/fulfilment/dan/', 'PayloadGeneratorController@findDAN');
Route::get('/find/fulfilment/submitinv', 'PayloadGeneratorController@submitInvoice');
Route::get('/find/simplequote/', 'PayloadGeneratorController@findSimpleQuote');
Route::get('/find/invoice/', 'PayloadGeneratorController@findInvoice');
Route::get('/find/fulfilment/do/', 'PayloadGeneratorController@findDO');
Route::get('/find/fulfilment/do/search/', 'PayloadGeneratorController@findDODetail');
Route::post('/find/fulfilment/do/search/', 'PayloadGeneratorController@findDODetail');
Route::get('/find/updatetracking/', 'FulfilmentController@getTrackingPayment');
Route::post('/find/updatetracking/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@getTrackingPayment',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::post('/update/trackingdiary/payment/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@updateTracking',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/phis150/', 'PHISController@itemPhis150');
Route::post('/find/phis150/', 'PHISController@itemPhis150');
Route::post('/find/updatephis/update/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@updateItemPhis150',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/find/phis160/', 'PHISController@itemPhis160');
Route::post('/find/phis160/', 'PHISController@itemPhis160');
Route::post('/find/updatephis160/update/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'PHISController@updateItemPhis160',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/requestnote/', 'PayloadGeneratorController@findRequestNote');
Route::get('/find/qt/scevaluation/', 'PayloadGeneratorController@findQTScEvaluation');
Route::get('/find/qt/scquotationtender/', 'PayloadGeneratorController@findQTQuotationTender');

Route::get('/dashboard/ejb/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@listMonitoringServiceRunningEjb',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/ejb/service/detail', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@getDetailParameterServiceRunningEjb',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/ejb/service/start', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@startServiceRunningEjb',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/ep-batch/ap511/update', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@reProcessFileAP511',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/find/ep-batch/ap511/invoice/update', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'WebServiceEpController@reProcessFileAP511ByInvoices',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/module/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@getDashboardModule',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/dashboard/crm/incident/itspec/module', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@dashboardCRMIncidentITSpecModule',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

// Day 1 - S1

Route::get('/list/crm/itModuleIncPendAckS1/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncPendAckS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncPendAckExceedS1/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncPendAckExceedS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncAcknowledgeS1/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncAcknowledgeS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncAcknowledgeExceedS1/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncAcknowledgeExceedS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

// Day 3 - S2

Route::get('/list/crm/itModuleIncPendAckS2/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncPendAckS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncPendAckExceedS2/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncPendAckExceedS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncAcknowledgeS2/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncAcknowledgeS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncAcknowledgeExceedS2/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncAcknowledgeExceedS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

//Day 5 - S3
Route::get('/list/crm/itModuleIncPendAckS3/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncPendAckS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncPendAckExceedS3/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncPendAckExceedS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncAcknowledgeS3/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncAcknowledgeS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleIncAcknowledgeExceedS3/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItModuleIncAcknowledgeExceedS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

//Module Selected - Other than IT Incident
Route::get('/list/crm/itModuleSelectedOtherThanITIncidentPendAck/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskSelectedModuleOtherThanITIncidentPendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleSelectedOtherThanITIncidentPendAckExceed/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskSelectedModuleOtherThanITIncidentPendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleSelectedOtherThanITIncidentAcknowledge/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskSelectedModuleOtherThanITIncidentAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itModuleSelectedOtherThanITIncidentAcknowledgeExceed/{SubCatID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskSelectedModuleOtherThanITIncidentAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);


// Module - Others
Route::get('/dashboard/crm/incident/itspec/module/others', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@dashboardCRMIncidentITSpecModuleOthers',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

// Day Other 1 Module - S1
Route::get('/list/crm/itOtherModuleIncPendAckS1', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncPendAckS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncPendAckExceedS1', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncPendAckExceedS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncAcknowledgeS1', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncAcknowledgeS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncAcknowledgeExceedS1', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncAcknowledgeExceedS1',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

// Day Other 3 Module  - S2
Route::get('/list/crm/itOtherModuleIncPendAckS2', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncPendAckS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncPendAckExceedS2', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncPendAckExceedS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncAcknowledgeS2', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncAcknowledgeS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncAcknowledgeExceedS2', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncAcknowledgeExceedS2',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

//Day Other 5 Module - S3
Route::get('/list/crm/itOtherModuleIncPendAckS3', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncPendAckS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncPendAckExceedS3', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncPendAckExceedS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncAcknowledgeS3', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncAcknowledgeS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOtherModuleIncAcknowledgeExceedS3', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOtherModuleIncAcknowledgeExceedS3',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

//Module - Others than IT Incident
Route::get('/list/crm/itOthersPendAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOthersModulePendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOthersPendAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOthersModulePendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOthersAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOthersModuleAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itOthersAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ModuleDashboardController@listTaskItOthersModuleAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/dashboard/crm/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@getDashboardCRM',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/dashboard/crm/incident', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncident',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/dashboard/crm/other', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@getDashboardCRMOthers',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/dashboard/crm/pmo', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@getDashboardCRMPMO',
    'roles' => array_merge(Config::get('constant.roles_it_specialist'), ['Group PMO']),
]);

Route::get('/dashboard/crm/topcases', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@getDashboardCRMTopCases',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/dashboard/crm/ageingcases', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@getDashboardCRMTotalAgeing',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/dashboard/statistic/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@getDashboardStatistic',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);

Route::get('/list/individualmodule/resolved/{userId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@getDetailsStatistic',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);
Route::get('/list/individualmodule/resolvedbydate/{userId}/{module}/{date}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@getDetailsStatisticByUserAndDate',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);
Route::get('/list/individualmodule/updateStatistic/{userId}/{fullName}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@updateDetailsStatistic',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);
Route::post('/list/individualmodule/updateStatisticValue', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@updateDetailsStatisticValue',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);
Route::get('/list/individualmodule/statistic/{userId}/{day}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@getDetailsStatisticSTL',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);
Route::get('/dashboard/statistic/moduleAssigned', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@dashboardStatisticDailyCasesResolved',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);
Route::get('/dashboard/statistic/moduleAssignedSTL', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'StatisticDashboardController@dashboardStatisticDailyCasesResolvedSTL',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);

// IT Coordinator - service
Route::get('/dashboard/crm/service/itcoord', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMServiceITCoord',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/itCoordServPendAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItCoordServPendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itCoordServPendAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItCoordServPendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itCoordServAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItCoordServAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itCoordServAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItCoordServAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

// other than incident assigned to IT Specialist
Route::get('/dashboard/crm/other/itspec', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMOthersAssignedToITSpec',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/itSpecOthersPendAck/{GroupID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecOthersPendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itSpecOthersPendAckExceed/{GroupID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecOthersPendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itSpecOthersAcknowledge/{GroupID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecOthersAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itSpecOthersAcknowledgeExceed/{GroupID}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecOthersAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

// IT Incident - Case Ageing
Route::get('/dashboard/crm/incident/caseageing', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentcaseAgeing',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

// IT Incident - Case Summary Ageing
Route::get('/dashboard/crm/incident/caseSummaryageing', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentcaseSummaryAgeing',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

// IT Coordinator - incident
Route::get('/dashboard/crm/incident/itcoord', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentITCoord',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);
///list/crm/detail/$GroupID/$ACKNOWLEDGE/s3
Route::get('/list/crm/detail/within/{groupId}/{status}/{flag}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardSlaDetailWithin',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/detail/exceed/{groupId}/{status}/{flag}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardSlaDetailExceed',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/itcoord/within/{status}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItCoordWithin',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itcoord/exceed/{status}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItCoordExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

// IT Specialist
Route::get('/dashboard/crm/incident/itspec', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentITSpec',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/itSpecIncPendAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecIncPendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itSpecIncPendAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecIncPendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itSpecIncAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecIncAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itSpecIncAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecIncAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

// IT Spec Severity
Route::get('/dashboard/crm/incident/itspec/severity', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentITSpecSeverity',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

// IT Spec Ageing
Route::get('/dashboard/crm/incident/itspec/ageing', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentITSpecAgeing',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

// PMO Ageing
Route::get('/dashboard/crm/incident/pmo/ageing', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentPMOAgeing',
    'roles' => array_merge(Config::get('constant.roles_it_specialist'), ['Group PMO']),
]);

// IT Redmine
Route::get('/dashboard/crm/incident/itspec/redmine', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentITSpecRedmine',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/itRedmineIncPendAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItRedmineIncPendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itRedmineIncPendAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItRedmineIncPendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itRedmineIncAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItRedmineIncAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itRedmineIncAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItRedmineIncAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

//incident Task from Approval
Route::get('/dashboard/crm/incident/itspec/approval', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMIncidentITSpecApproval',
    'roles' => array_merge(Config::get('constant.roles_it_specialist')),
]);

Route::get('/list/crm/itApprovalIncPendAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApproverIncPendAck',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itApprovalIncPendAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApproverIncPendAckExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itApprovalIncAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApprovalIncAcknowledge',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itApprovalIncAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApprovalIncAcknowledgeExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itApprovalIncReassigned', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApprovalIncReassigned',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/list/crm/itApprovalIncReassignedExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApprovalIncReassignedExceed',
    'roles' => Config::get('constant.roles_it_specialist'),
]);

Route::get('/dashboard/crm/email-inbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@showCrmSchedulerEmailInbound',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);

Route::get('/dashboard/crm/slacs', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMCS',
    'roles' => array_merge(Config::get('constant.roles_adv_ep')),
]);

Route::group([
    'prefix' => '/dashboard/crm/cs',
    // 'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep'))
], function () {
    Route::get('/main', ['uses' => 'CrmDashboard\CustomerService@getDashboardCS']);
    Route::get('/pendinginput', ['uses' => 'CrmDashboard\CustomerService@getDashboardCSPendInput']);
    Route::get('/pendinginput/{contactmode}/{info}', ['uses' => 'CrmDashboard\CustomerService@getDashboardCSPendInputDetail']);
    Route::get('/talkdesk', ['uses' => 'Poms\TalkdeskController@main']);
    Route::post('/talkdesk', ['uses' => 'Poms\TalkdeskController@main']);
    Route::post('/talkdesk/upload', ['uses' => 'Poms\TalkdeskController@uploadData']);
});

Route::get('/dashboard/cs/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@getDashboardCS',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/dashboard/cs/email-inbound', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@showCrmSchedulerEmailInbound',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

// cs request to open the permission to all cs can update the email inbound
Route::post('/dashboard/cs/update-jobque', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMController@updateJobQue',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/dashboard/cs/slacs', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardCRMCS',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/dashboard/cs/backdated/pendingresponse', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardPendingResponse',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/backdatedpendingresponse', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@backdatedPendingResponseOP',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email/backdatedpendingresponse', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@backdatedPendingResponseEmail',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/withinsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofCasesWithinCreatedOpenPortal',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/exceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofCasesExceedCreatedOpenPortal',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofCasesCreatedEmail',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/responsewithinsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofOpenPortalCasesResponseWithinSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email/responsewithinsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofEmailCasesResponseWithinSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/noresponsewithinsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofOpenPortalCasesNoResponseWithinSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email/noresponsewithinsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofEmailCasesNoResponseWithinSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/responseexceedsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofOpenPortalCasesResponseExceedSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email/responseexceedsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofEmailCasesResponseExceedSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/noresponseexceedsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofOpenPortalCasesNoResponseExceedSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email/noresponseexceedsla', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofEmailCasesNoResponseExceedSLA',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/openportal/noresponsewithinslabyScheduler', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofOpenPortalCasesNoResponseWithinSLAByScheduler',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/list/crm/totalcases/email/noresponsewithinslabyScheduler', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listofEmailCasesNoResponseWithinSLAByScheduler',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::post('/dashboard/crm/update-jobque', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMController@updateJobQue',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.users_adv_crm')),
]);


/*** Start App for App Support cause by Data Issue on eP (Monitoring to fix) ***/
Route::group([
    'prefix' => '/app-support/',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'))
], function () {
    Route::get('mof_deleted_to_active', ['uses' => 'AppSupport\SmDataIssueController@findListMofDeletedButActuallyActive']);
    Route::get('mof_active_to_deleted', ['uses' => 'AppSupport\SmDataIssueController@findListMofActiveButActuallyDeleted']);
    Route::get('mof_active_to_deleted/sql', ['uses' => 'AppSupport\SmDataIssueController@getMofActiveToDeletedSql']);
    Route::get('softcert-user-not-sync', ['uses' => 'AppSupport\SmDataIssueController@getSoftcertNotSync']);
    Route::get('personnel_is_bumi_null', ['uses' => 'AppSupport\SmDataIssueController@getListPersonnelIsBumiNull']);
    Route::get('application_with_no_category', ['uses' => 'AppSupport\SmDataIssueController@getListApplicationWithNoCategory']);
    Route::get('app_missing_sv', ['uses' => 'AppSupport\SmDataIssueController@getAppMissingSV']);
    // Route::get('supplier_disciplinary_action', ['uses' => 'AppSupport\SmDataIssueController@getLisSupplierDisciplinaryAction']);
    Route::get('bumi_status_cancellation', ['uses' => 'AppSupport\SmDataIssueController@getBumiStatusCancellation']);
    Route::get('site_visit_rollback', ['uses' => 'AppSupport\SmDataIssueController@findSiteVisitRollback']);
    Route::get('application_status_not_sync', ['uses' => 'AppSupport\SmDataIssueController@getApplicationStatusNotSync']);
    Route::get('users_inactive_not_sync', ['uses' => 'AppSupport\PmUserIssueController@findListUsersNotSyncAsInactive']);
    Route::get('reporting', ['uses' => 'AppSupport\SmDataIssueController@getReportingList']);
    Route::post('reporting', ['uses' => 'AppSupport\SmDataIssueController@getReportVariable']);
    Route::get('reporting/list/module/{module}', ['uses' => 'AppSupport\SmDataIssueController@listModuleDetails']);
    Route::get('reporting/list/report/name/{name}', ['uses' => 'AppSupport\SmDataIssueController@listFieldName']);
    Route::get('ptj_org_validaty_rollback', ['uses' => 'AppSupport\SmDataIssueController@getPTJOrgValidityRollback']);
    Route::get('pm/user/validate', ['uses' => 'AppSupport\PmUserIssueController@validateUserByStatus']);
    Route::post('pm/user/deactivate', ['uses' => 'AppSupport\PmUserIssueController@deactivateUser']);
    Route::get('file/upload-mbb', ['uses' => 'AppSupport\SmReceiptMbbController@initiateUploadMbbFile']);
    Route::post('file/upload-mbb', ['uses' => 'AppSupport\SmReceiptMbbController@uploadMbbFile']);
    Route::get('mof_cert_exceed_3_years', ['uses' => 'AppSupport\SmDataIssueController@getListMOFCertExceed3Years']);
    Route::get('payment-razer', ['uses' => 'AppSupport\SmDataIssueController@getListPaymentByRazer']);
    Route::post('payment-razer', ['uses' => 'AppSupport\SmDataIssueController@updatePaymentRazerBinType']);
    //S4 CRM Case Monitoring
    Route::get('s4-crm-case-monitoring', ['uses' => 'CrmDashboard\ReportCrmController@s4CrmCaseMonitoring']);
    Route::get('supplier_disciplinary_action_app', ['uses' => 'AppSupport\SmDataIssueController@getAppSupplierDisciplinaryAction']);
    Route::get('report/all/module', ['uses' => 'AppSupport\SmDataIssueController@getModuleFull']);
    Route::get('report/all/module/{module}', ['uses' => 'AppSupport\SmDataIssueController@getReportsByModule']);
    Route::post('/search-reports', ['uses' => 'AppSupport\SmDataIssueController@searchReports'])->name('search.reports');
    // Route::match(['get', 'post'], '/search-reports', ['uses' => 'AppSupport\SmDataIssueController@searchReports'])
    //  ->name('search.reports');
});
//App Support Dashboard
Route::group([
    'prefix' => '/app-support/dashboard',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_patch_ep'))
], function() {
    Route::get('/pm', ['uses' => 'AppSupport\PmRoleIssueController@displayPmDashboardView']);
    Route::get('/duplicate-role-error', ['uses' => 'AppSupport\PmRoleIssueController@getDuplicateRoleError']);
    Route::get('/user-role-issue', ['uses' => 'AppSupport\PmRoleIssueController@getUsersWithRoleIssues']);
    Route::get('/duplicate-role-error-detail/{userId}/{roleCode}', ['uses' => 'AppSupport\PmRoleIssueController@getDuplicateRoleErrorDetail']);
    Route::get('/sm', ['uses' => 'AppSupport\SmDataIssueController@displaySmDashboardView']);
    Route::get('/supplier-register-not-exist-sap', ['uses' => 'AppSupport\SmDataIssueController@getSupplierRegisterNotExistSAPError']);
    Route::get('/list-supplier-register-not-exist-sap/{supplierType}', ['uses' => 'AppSupport\SmDataIssueController@getListSupplierRegisterNotExistSAPError']);
    Route::get('/monitor-sm-data-issue', ['uses' => 'AppSupport\SmDataIssueController@getCountSmDataIssue']);
});
Route::group([
    'prefix' => '/app-support/fix/',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_patch_ep'))
], function () {
    Route::get('bumi_status_cancellation', ['uses' => 'AppSupport\SmDataIssueController@fixIssueBumiStatusCancellation']);
    Route::get('app_missing_sv/{appl_no}', ['uses' => 'AppSupport\SmDataIssueController@updateAppMissingSV']);
});


Route::get('/stl', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@getDashboardSTL',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringSM', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringSM',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringQT', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringQT',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringDPSQ', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringDPSQ',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringDPRN', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringDPRN',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringDPCodify', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringDPCodify',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringRNTriggerOrder', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringRNTriggerOrder',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringPRCRInitiate', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringPRCRInitiate',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitorinDO', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitorinDO',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/checkSTLMonitorinIntegrationPRCR', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitorinIntegrationPRCR',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/checkSTLMonitorinIntegrationFRN', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitorinIntegrationFRN',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/checkSTLMonitorinIntegrationDAN', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitorinIntegrationDAN',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/checkSTLMonitorinIntegrationSD', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitorinIntegrationSD',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/checkSTLMonitorinIntegrationPA', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitorinIntegrationPA',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLMonitoringFLApproverPRCR', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringFLApproverPRCR',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLPendingInvoiceCreation', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLPendingInvoiceCreation',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLPendingPaymentMatch', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLPendingPaymentMatch',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLPendingPrCrReview', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLPendingPrCrReview',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLQueryPRCR', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLQueryPRCR',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLAwaitingIgfmasPRCR', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLAwaitingIgfmasPRCR',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLOrderPOCO', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLOrderPOCO',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLFLDebit', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLFLDebit',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLFrnAwaitingIgfmas', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLFrnAwaitingIgfmas',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
Route::get('/dashboard/checkSTLPendingRevisionApprovalPRCR', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLPendingRevisionApprovalPRCR',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/checkSTLCancelPOCO', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLCancelPOCO',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/checkSTLPRCREpp13', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringPRCREpp13Y',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/checkStuckTaskPRCRMM501DEL', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringPRCRMM501Del',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/checkStuckFLPendingPaymentEpp017Y', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringFLPendingPaymentEpp017Y',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/checkStuckYEPCF', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkSTLMonitoringStuckYEPCF',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/checkDuplicateInvoice', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'STLDashboardController@checkDuplicateInvoice',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/dashboard/cs/topEnqSubCategory', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardTopEnqSubCategory',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/dashboard/top-enq/download/{subCat}/{subCat2}', 'CRMDashboardController@downloadTopEnq');

Route::get('/report/login/download/{year}', 'AppSchedulerController@downloadReportMonthly');

Route::get('dashboard/cs/topIncServSubCategory', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@dashboardTopIncServSubCategory',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_cs_ep')),
]);

Route::get('/dashboard/top-incserv/download/{subCat}/{subCat2}', 'CRMDashboardController@downloadTopIncServ');

Route::get('/dashboard/data/lookup', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@searchDatatableInePTable',
    'roles' => array_merge(Config::get('constant.roles_ep_operation'), Config::get('constant.roles_specialist')),
]);

Route::get('/search/table/{data}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@searchTable',
    'roles' => array_merge(Config::get('constant.roles_ep_operation'), Config::get('constant.roles_specialist')),
]);

Route::get('/eperolehan/download/data/lookup/{data}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'DashboardController@downloadData',
    'roles' => array_merge(Config::get('constant.roles_ep_operation'), Config::get('constant.roles_specialist')),
]);

Route::get('/list/crm/itIncPendAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItIncPendAck',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itIncAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItIncAcknowledge',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecialist', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecialist',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecPendingAck', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecPendingAck',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecAcknowledge', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecAcknowledge',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itApprover', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApprover',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecAfterApprover', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskSpecAfterApprover',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itIncPendAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItIncPendAckExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itIncAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItIncAcknowledgeExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecialistExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecialistExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecPendingAckExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecPendingAckExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecAcknowledgeExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItSpecAcknowledgeExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itApproverExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskItApproverExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/list/crm/itSpecAfterApproverExceed', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'CRMDashboardController@listTaskSpecAfterApproverExceed',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::get('/crm/guideline/stuck_gfmas/{fileName}', function ($fileName) {
    $headers = [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        'Cache-Control' => 'max-age=0',
    ];

    $fullPath = storage_path('/stuck_igfmas/' . $fileName);
    return response()->download($fullPath, $fileName, $headers);
});

Route::get('/find/dp-summary/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@getDPSummary',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/dp-summary/item/{request_item_id}/{request_note_no}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@clickList',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/dp-summary/item/approver/{group_code}/{request_note_no}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@approverList',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/fl-summary/fl/approver/{group_code}/{doc_no}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@flApproverList',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/direct_purchase_workflow/{docid}/{doctype}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@dpWorkflowSummary',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist')),
]);

Route::get('/find/fulfilment_workflow/{docid}/{doctype}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@flWorkflowSummary',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist')),
]);

Route::get('/find/fn-summary/ro/approver/{{$row[0]["docNumber"]}}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@approverListRO',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/fn-summary/dofn/{doc_no}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@findSummaryWFDOFN',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/find/fn-summary/', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'FulfilmentController@getFNSummary',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.users_cr_mgmt'), Config::get('constant.roles_ep_operation')),
]);

//Knowage Resouces Integration
Route::group([
    'prefix' => 'knowage',
    // 'middleware' => ['auth', 'roles'],
    'roles' => Config::get('constant.roles_adv_ep')
], function () {
    Route::get('/', ['uses' => 'KnowageResourcesController@index']);
    Route::group([
        'prefix' => 'razerpay'
    ], function () {
        Route::get('/', ['uses' => 'KnowageResourcesController@index']);
        Route::get('/transaction', ['uses' => 'KnowageResourcesController@RazerPayTransaction']);
        Route::get('/transaction-view/{razerid}', 'KnowageResourcesController@AjaxRazerTransactionDetail');
        Route::post('/transaction-overwrite', 'KnowageResourcesController@AjaxRazerTransactionOverwrite');
        Route::post('/transaction-upload', 'KnowageResourcesController@AjaxRazerTransactionUpload');
    });
});

//Middlewaare Automation
Route::group([
    'prefix' => '/automation',
    'roles' => Config::get('constant.roles_adv_ep')
], function () {
    Route::group(['prefix' => '/ep-portal'], function () {
        Route::get('/', ['uses' => 'AutomationController@index']);
        Route::get('/clear-cookies/{refcode}', ['uses' => 'AutomationController@ClearCookies']);
        Route::get('/sse/{refcode}', ['uses' => 'AutomationController@Sse']);
    });
});

Route::get('/gpki/test', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GPKIController@test',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/gpki/sign', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GPKIController@sign',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/gpki/verify', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GPKIController@verify',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/gpki/user-signing-list', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GPKIController@userSigningList',
    'roles' => Config::get('constant.roles_adv_ep'),
])->name('user.signing.list');

Route::get('/gpki/user-status-count', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GPKIController@userStatusCount',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::match(['get', 'post'], '/gpki/user-signing-list-csv', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'GPKIController@userSigningListCSV',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

//SPKI TRUSTGATE
Route::group([
    'prefix' => 'spki/trustgate',
    'middleware' => ['auth', 'roles'],
    'roles' => Config::get('constant.roles_adv_ep')
], function () {
    Route::get('/', ['uses' => 'Spki\TrustgateController@singningTrustgate']);
    Route::post('/', ['uses' => 'Spki\TrustgateController@singningTrustgate']);
    Route::post('/signin', ['uses' => 'Spki\TrustgateController@signInData']);
    Route::get('/challengequestion', ['uses' => 'Spki\TrustgateController@getChallengeQuestion']);
    Route::post('/challengequestion', ['uses' => 'Spki\TrustgateController@getChallengeQuestion']);
    Route::get('/resetpin', ['uses' => 'Spki\TrustgateController@resetPin']);
    Route::post('/resetpin', ['uses' => 'Spki\TrustgateController@resetPin']);
    Route::get('/updatechallengequestion', ['uses' => 'Spki\TrustgateController@updateChallengeQuestion']);
    Route::post('/updatechallengequestion', ['uses' => 'Spki\TrustgateController@updateChallengeQuestion']);
    Route::get('/changepin', ['uses' => 'Spki\TrustgateController@changePin']);
    Route::post('/changepin', ['uses' => 'Spki\TrustgateController@changePin']);
    Route::get('/revoke', ['uses' => 'Spki\TrustgateController@revoke']);
    Route::post('/revoke', ['uses' => 'Spki\TrustgateController@revoke']);
});

//SPKI DIGICERT
Route::group([
    'prefix' => 'spki/digicert',
    'middleware' => ['auth', 'roles'],
    'roles' => Config::get('constant.roles_adv_ep')
], function () {
    Route::get('/challengequestion', ['uses' => 'Spki\DigicertController@getChallengeQuestion']);
    Route::post('/challengequestion', ['uses' => 'Spki\DigicertController@getChallengeQuestion']);
    Route::get('/updatechallengequestion', ['uses' => 'Spki\DigicertController@updateChallengeQuestion']);
    Route::post('/updatechallengequestion', ['uses' => 'Spki\DigicertController@updateChallengeQuestion']);
    Route::get('/changepin', ['uses' => 'Spki\DigicertController@changePin']);
    Route::post('/changepin', ['uses' => 'Spki\DigicertController@changePin']);
    Route::get('/resetpin', ['uses' => 'Spki\DigicertController@resetPin']);
    Route::post('/resetpin', ['uses' => 'Spki\DigicertController@resetPin']);
    Route::get('/signing', ['uses' => 'Spki\DigicertController@signing']);
    Route::post('/signing', ['uses' => 'Spki\DigicertController@signing']);
    Route::post('/signing/data', ['uses' => 'Spki\DigicertController@signingData']);
    Route::get('/revoke', ['uses' => 'Spki\DigicertController@revoke']);
    Route::post('/revoke', ['uses' => 'Spki\DigicertController@revoke']);
});

/*** Start App for STL CRM
 * 1) SLA
 * 2) DUPLICATE TASK
 * ***/
Route::group([
    'prefix' => '/stl/crm',
    'middleware' => ['auth', 'roles'],
    'roles' => Config::get('constant.roles_stl_crm')
], function () {
    Route::get('', ['uses' => 'STLCRMDashboardController@main']);
    Route::post('', ['uses' => 'STLCRMDashboardController@main']);
    Route::post('/update', ['uses' => 'STLCRMDashboardController@update']);
    Route::get('/duplicatetask', ['uses' => 'STLCRMDashboardController@duplicateTask']);
    Route::post('/duplicatetask', ['uses' => 'STLCRMDashboardController@duplicateTask']);
    Route::get('/taskdetail', ['uses' => 'STLCRMDashboardController@taskDetail']);
    Route::post('/duplicatetask/delete', ['uses' => 'STLCRMDashboardController@updateTaskDetail']);
    Route::get('/sla', ['uses' => 'STLCRMDashboardController@caseSlaDetail']);
    Route::post('/sla', ['uses' => 'STLCRMDashboardController@caseSlaDetail']);
    Route::post('/sla/update', ['uses' => 'STLCRMDashboardController@caseSlaUpdate']);
    Route::post('/sla/updateresolution', ['uses' => 'STLCRMDashboardController@caseSlaUpdateResolution']);
});
/*** Start App for Master Data
 * 1) INTERFACE LOG
 * 2) PROJECT
 * ***/
Route::group([
    'prefix' => '/find/masterdata',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_operation'))
], function () {
    Route::get('/project', ['uses' => 'MasterDataController@project']);
    Route::post('/project', ['uses' => 'MasterDataController@projectDetail']);
    Route::get('/interfacelog', ['uses' => 'MasterDataController@interfacelog']);
    Route::post('/interfacelog', ['uses' => 'MasterDataController@interfacelog']);
    Route::get('/ptj', ['uses' => 'MasterDataController@ptj']);
    Route::post('/ptj', ['uses' => 'MasterDataController@ptjDetail']);
    Route::get('/kumpptj', ['uses' => 'MasterDataController@kumpptj']);
    Route::post('/kumpptj', ['uses' => 'MasterDataController@kumpptjDetail']);
    Route::get('/pegpengawal', ['uses' => 'MasterDataController@pegpengawal']);
    Route::post('/pegpengawal', ['uses' => 'MasterDataController@pegpengawalDetail']);
    Route::get('/vot', ['uses' => 'MasterDataController@vot']);
    Route::post('/vot', ['uses' => 'MasterDataController@votDetail']);
    Route::get('/vot/{fundtype}/{fundid}', ['uses' => 'MasterDataController@votType']);
    Route::get('/glaccount', ['uses' => 'MasterDataController@glaccount']);
    Route::post('/glaccount', ['uses' => 'MasterDataController@glaccountDetail']);
    Route::get('/programactivity', ['uses' => 'MasterDataController@programactivity']);
    Route::post('/programactivity', ['uses' => 'MasterDataController@programactivityDetail']);
    Route::get('/agoffice', ['uses' => 'MasterDataController@agoffice']);
    Route::post('/agoffice', ['uses' => 'MasterDataController@agofficeDetail']);
    Route::get('/dana', ['uses' => 'MasterDataController@dana']);
    Route::post('/dana', ['uses' => 'MasterDataController@danaDetail']);
    Route::get('/ep', ['uses' => 'MasterDataController@epData']);
    Route::post('/ep', ['uses' => 'MasterDataController@epData']);
    Route::get('/ep/epdetail', ['uses' => 'MasterDataController@epDataDetail']);
});

/**  ACCESS FROM HELPDESK TO DOWNLOAD A FILE */
Route::get('/prod-support/download/data-patch/{token}', function ($token) {

    $objDataFix = DB::connection('mysql_ep_prod_support')->table('ps_data_fix')->where('token_access', $token)
        ->first();
    if ($objDataFix) {
        $folderZipPath = $objDataFix->folder_zip_path;
        $folderName = $objDataFix->folder_zip_name;
        $zipPathName = $zipPathName = $folderZipPath . '/' . $folderName;
        try {
            return response()->download(storage_path($zipPathName));
        } catch (Exception $ex) {
            return $ex->getMessage();
        }
    }
    return "No record found!";
});

/** OPEN API FOR HELPDESK REQUEST TO SENT IT REQUEST BY TOKEN DTL FIX  */
Route::get('/prod-support/request/helpdesk/{token}', function ($token) {
    $thisCls = new App\Migrate\ProdSupport\HelpdeskIntegration;
    return $thisCls->retrieveAndSendHelpDeskByEachFixDtl($token);
});

/** MANUAL TRIGGER IF NOT SUCCESS SENT TO HELPDESK */
Route::get('/prod-support/data-patching/sent-helpdesk/{dataFixId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsPatchingController@sendHelpDeskDataPatchRequest',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
/** MANUAL TRIGGER IF NOT SUCCESS SENT TO HELPDESK */
Route::get('/prod-support/data-patching/sent-helpdesk-each-dtl/{dataFixId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsPatchingController@sendHelpDeskDataPatchRequestByEachPatchFixDetail',
    'roles' => Config::get('constant.roles_adv_ep'),
]);
/** MANUAL TRIGGER IF NOT SUCCESS SENT TO HELPDESK -> specific detail patch ID */
Route::get('/prod-support/data-patching/sent-helpdesk-specific-dtl/{dataFixId}/{dataFixDetailId}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsPatchingController@sendHelpDeskDataPatchRequestByFixPatchDetail',
    'roles' => Config::get('constant.roles_adv_ep'),
]);


Route::get('/prod-support/download/data-patch-detail/{token}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsPatchingController@downloadDocDataFixDtlIdByToken',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

/*** Start App for Prod SupportFeatures 
 * 1) Data Patch Management
 * ***/
Route::group([
    'prefix' => '/prod-support',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_operation'))
], function () {
    Route::get('/data-patching', ['uses' => 'ProdSupport\PsPatchingController@listDataPacthing']);
    Route::post('/data-patching', ['uses' => 'ProdSupport\PsPatchingController@listDataPacthing']);
    //    Route::get('/data-patching/cancelsubmit/{datafixid}', ['uses' => 'ProdSupport\PsPatchingController@updatePortingTodayCancelSubmit']);
    Route::post('/data-patching/cancelporting/{id}', ['uses' => 'ProdSupport\PsPatchingController@deletePortingToday']);
    Route::post('/data-patching/createport', ['uses' => 'ProdSupport\PsPatchingController@createPortSchedulePorting']);
    Route::post('/data-patching/create', ['uses' => 'ProdSupport\PsPatchingController@createPatching']);
    Route::get('/data-patching/crmnumber/{carian}', ['uses' => 'ProdSupport\PsPatchingController@getCrmnumber']);
    Route::get('/data-patching/download/{dataFixId}', ['uses' => 'ProdSupport\PsPatchingController@createWordandFile']);
    Route::get('/data-patching/emailtoapprover', ['uses' => 'ProdSupport\PsPatchingController@sendEmailtoApprover']);
    Route::get('/data-patching/closed/{datafixid}', ['uses' => 'ProdSupport\PsPatchingController@updatePortingToClosed']);
    //    Route::get('/data-patching-urgent/closed/{datafixid}', ['uses' => 'ProdSupport\PsPatchingController@closedPortingTodayurgent']);
    Route::get('/data-patching-urgent/cancelsubmit/{datafixid}', ['uses' => 'ProdSupport\PsPatchingController@cancelSubmitPortingTodayurgent']);
    Route::get('/edit-patch/{dataFixDtlId}', ['uses' => 'ProdSupport\PsPatchingController@findDetailsDataPatch']);
    Route::get('/edit-patch/redmine/{redmineno}', ['uses' => 'ProdSupport\PsPatchingController@editListDataredmineDetails']);
    Route::get('/urgent-patching', ['uses' => 'ProdSupport\PsPatchingController@listDataPacthingUrgent']);
    Route::post('/urgent-patching/create', ['uses' => 'ProdSupport\PsPatchingController@createPatchingUrgent']);
    Route::post('/urgent-patching', ['uses' => 'ProdSupport\PsPatchingController@listDataPacthingUrgent']);
    Route::post('/urgent-patching', ['uses' => 'ProdSupport\PsPatchingController@actionForDeleteAndUpdatePorting']);
    Route::post('/urgent-patching/createport', ['uses' => 'ProdSupport\PsPatchingController@createPort']);
    //    Route::get('/urgent-patching/crmnumber/{carian}', ['uses' => 'ProdSupport\PsPatchingController@getCrmnumber']);
    Route::get('/data-patching/problemtype/{carian}', ['uses' => 'ProdSupport\PsPatchingController@getProblemType']);
    //    Route::get('/urgent-patching/problemtype/{carian}', ['uses' => 'ProdSupport\PsPatchingController@getProblemType']);
    Route::get('/data-patching/module/{module}', ['uses' => 'ProdSupport\PsPatchingController@getListProbDescription']);
    Route::get('/data-lookup/{probdescid}', ['uses' => 'ProdSupport\PsPatchingController@getListProbDescForChange']);
    //    Route::get('/urgent-patching/module/{module}', ['uses' => 'ProdSupport\PsPatchingController@getListProbDescription']);
    Route::get('/history-data-patch', ['uses' => 'ProdSupport\PsPatchingController@getListAll']);
    Route::post('/history-data-patch', ['uses' => 'ProdSupport\PsPatchingController@getListAll']);
    Route::get('/history-data-patching/download/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@createWordandFileByDataFixDtlId']);
    Route::get('/history-data-patching/view/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@findDetailsDataPatch']);
    Route::get('/reject_data_patch/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@rejectDataPatch']);
    Route::get('/reject_data_patch/urgent/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@rejectDataPatchUrgent']);
    Route::post('/reporting', ['uses' => 'ProdSupport\PsPatchingController@downloadReport']);
    Route::get('/reporting', ['uses' => 'ProdSupport\PsPatchingController@downloadReport']);
    Route::get('/data-lookup', ['uses' => 'ProdSupport\PsPatchingController@listDataLookup']);
    Route::post('/add-data-lookup/create', ['uses' => 'ProdSupport\PsPatchingController@addlistDataLookup']);
    Route::post('/edit-data-lookup', ['uses' => 'ProdSupport\PsPatchingController@editDataLookup']);
    Route::get('/delete-data-lookup/{id}', ['uses' => 'ProdSupport\PsPatchingController@cancelDataLookup']);
    Route::post('/delete-data-lookup/{id}', ['uses' => 'ProdSupport\PsPatchingController@cancelDataLookup']);
    Route::get('/approver', ['uses' => 'ProdSupport\PsPatchingController@approverUser']);
    Route::post('/approver/create', ['uses' => 'ProdSupport\PsPatchingController@addNewApproverUser']);
    Route::post('/edit-approver', ['uses' => 'ProdSupport\PsPatchingController@editApproverDetails']);
    Route::get('/delete-approver/{id}', ['uses' => 'ProdSupport\PsPatchingController@cancelApprover']);
    Route::get('/edit-patch-urgent/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@editListDataPatchDetailsUrgent']);
    Route::post('/edit-patch-urgent/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@updatePatchingListTodayUrgent']);
    Route::post('/edit-patch/update', ['uses' => 'ProdSupport\PsPatchingController@actionForSubmitUpdateAndView']);
    Route::post('/edit-patching/createscript/{datafixdtlid}', ['uses' => 'ProdSupport\PsPatchingController@createScriptPatching']);
    Route::post('/edit-patching/delete-script/{dataFixDtlId}', ['uses' => 'ProdSupport\PsPatchingController@deleteScriptPatching']);
    Route::post('/edit-patching-urgent/createscript/{dataFixDtlId}', ['uses' => 'ProdSupport\PsPatchingController@createScriptPatchingUrgent']);
    Route::get('/data-patch/remove/{dataFixDtlId}', ['uses' => 'ProdSupport\PsPatchingController@deleteDataPatchDtl']);
    Route::get('/change-request', ['uses' => 'ProdSupport\PsPatchingController@getListAll']);
    Route::get('/patch-script', ['uses' => 'ProdSupport\PsPatchingController@getListAll']);
    Route::post('/upload_attachment/{crmid}', ['uses' => 'ProdSupport\PsPatchingController@uploadAttachment']);
    Route::post('/upload_attachment/delete_attachment/{dataFixDtlId}', ['uses' => 'ProdSupport\PsPatchingController@deleteUploadAttachment']);
    Route::get('/repatch/crmid/{byfunction}/{datafixdtlid}/{porting}', ['uses' => 'ProdSupport\PsPatchingController@createNewPortingAfterCancelled']);
});

/*** Start App for Prod SupportFeatures 
 * Defect Testing Management
 * ***/
Route::group([
    'prefix' => '/prod-support/defect_testing',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_patch_ep'))
], function () {
    Route::post('/', ['uses' => 'ProdSupport\PsDefectEpController@sirNumberFromRedmine']);
    Route::get('/', ['uses' => 'ProdSupport\PsDefectEpController@sirNumberFromRedmine']);
    Route::get('/{defectid}', ['uses' => 'ProdSupport\PsDefectEpController@sirNumberFromRedmine']);
    Route::post('/add_date/{defectid}', ['uses' => 'ProdSupport\PsDefectEpController@addDateTesting']);
    Route::get('/add_date/{defectid}', ['uses' => 'ProdSupport\PsDefectEpController@listTestingDate']);
    Route::post('/edit_date/{defectdatetid}', ['uses' => 'ProdSupport\PsDefectEpController@editDateTesting']);
    Route::post('/delete_date/{editdateid}', ['uses' => 'ProdSupport\PsDefectEpController@deleteDateTesting']);
    Route::get('/redmine_no/{carian}', ['uses' => 'ProdSupport\PsDefectEpController@testingFromRedmine']);
    Route::get('/redmine_no/{defectid}', ['uses' => 'ProdSupport\PsDefectEpController@testingFromRedmine']);
    Route::get('/status/{status}/{carian}/{deploy}', ['uses' => 'ProdSupport\PsDefectEpController@listDetailByFilterStatus']);
    Route::get('/status_closed/{carian}', ['uses' => 'ProdSupport\PsDefectEpController@listDetailByFilterStatusClosed']);
    Route::post('/download', ['uses' => 'ProdSupport\PsDefectEpController@downloadReport']);
    Route::get('/download', ['uses' => 'ProdSupport\PsDefectEpController@downloadReport']);
});

/*** Start App for Prod SupportFeatures 
 * Report Management
 * ***/
//
Route::get('/report/management', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@reportManagement',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);
Route::post('/report/management', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@reportManagement',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/report/ketidakpatuhan/cptpp', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@nonComplianceCptpp',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
])->name('nonComplianceCptpp');
Route::get('/report/ketidakpatuhan/cptpp/{startDate}/{toDate}/{module?}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@nonComplianceCptppForModule',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
])->where('module', 'sm|pp|qt|ct|fl');
Route::get('/report/edit-report/{report_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@editReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::post('/report/edit-report/createscript/{report_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@createScriptReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::post('/report/edit-report/delete-script/{report_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@deleteScriptReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::post('/report/edit-report/upload-attachment/{report_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@createAttachmentReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/report/edit-report/script/download/{script_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@downloadScriptReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::get('/report/edit-report/attachment/download/{result_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@downloadAttachmentReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::post('/report/edit-report/delete-attachment/{result_id}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ReportManagement\ReportManagementController@deleteAttacmentReport',
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
]);

Route::group([
    'prefix' => '/report/matrix',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_patch_ep'), Config::get('constant.roles_ep_specialist'), Config::get('constant.roles_ep_operation')),
], function () {
    Route::get('/', ['uses' => 'ReportManagement\ReportManagementController@securityMatrixSummaryView']);
    Route::post('/spki-data', ['uses' => 'ReportManagement\ReportManagementController@securityMatrixSummarySpkiData']);
    Route::post('/ap511-data', ['uses' => 'ReportManagement\ReportManagementController@securityMatrixSummaryAp511Data']);
    Route::post('/apive-data', ['uses' => 'ReportManagement\ReportManagementController@securityMatrixSummaryApiveData']);
});

Route::group([
    'prefix' => '/prod-support/rpt',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
], function () {
    //Route::post('/', ['uses' => 'ProdSupport\PsDefectEpController@sirNumberFromRedmine']);
    Route::get('/data_lookup', ['uses' => 'ProdSupport\PsReportController@reportDataLookup']);
    Route::post('/data_lookup', ['uses' => 'ProdSupport\PsReportController@reportDataLookup']);
    Route::get('/delete-data-lookup/{id}', ['uses' => 'ProdSupport\PsReportController@cancelDataLookup']);
    Route::get('/report_001_byyear', ['uses' => 'ProdSupport\PsReportController@ReadStatbyyear']);
    Route::post('/report_001_byyear', ['uses' => 'ProdSupport\PsReportController@actionForSearchOrDownload']);
    Route::post('/report_001_byyear/download', ['uses' => 'ProdSupport\PsReportController@downloadFromList']);
    Route::get('/report_001_updatedform', ['uses' => 'ProdSupport\PsReportController@ReadStatbyMonthYear']);
    Route::get('/report_001_updatedform/edit/{editid}', ['uses' => 'ProdSupport\PsReportController@ReadStatbyMonthYear']);
    Route::post('/report_001_updatedform', ['uses' => 'ProdSupport\PsReportController@ReadStatbyMonthYear']);
    Route::post('/report_001_updatedform1/{id}', ['uses' => 'ProdSupport\PsReportController@UpdatedStatReport_001']);
    Route::get('/summary', ['uses' => 'ProdSupport\PsReportController@reportSummary']);
    Route::post('/summary', ['uses' => 'ProdSupport\PsReportController@reportSummary']);
    Route::post('/report_001_statistic', ['uses' => 'ProdSupport\PsReportController@createStatisticInfo']);
    Route::get('/report_001_statistic', ['uses' => 'ProdSupport\PsReportController@createStatisticInfo']);
});

Route::get('/activity/admin/ep', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsReportController@getAdminEpActivity',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::post('/activity/admin/ep', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsReportController@listAdminEpActivity',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);
Route::get('/activity/admin/ep/download/{startdate}/{enddate}', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsReportController@downloadListAdminEpActivity',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

//For user integration report
Route::get('/report/ep/integration', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsReportController@integrationReporteP',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::post('/report/ep/integration', [
    'middleware' => ['auth', 'roles'],
    'uses' => 'ProdSupport\PsReportController@integrationReporteP',
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
]);

Route::group([
    'prefix' => '/prod-support/',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation')),
], function () {
    Route::post('reporting_applied/createscript/{reportid}', ['uses' => 'ProdSupport\PsReportController@reportAddScript']);
    Route::post('reporting_applied/upload_excel/{reportid}', ['uses' => 'ProdSupport\PsReportController@reportAddExcel']);
    Route::get('report_document/carian/{id}', ['uses' => 'ProdSupport\PsReportController@listAllDocumentByReportId']);
    Route::get('reporting/download_file/{scriptid}', ['uses' => 'ProdSupport\PsReportController@downloadFile']);
    Route::post('report/delete_document/{docid}', ['uses' => 'ProdSupport\PsReportController@deleteDocumentforReporting']);
    Route::get('report/delete/{idno}', ['uses' => 'ProdSupport\PsReportController@deleteReporting']);
    Route::get('report/view_more_summary/{idno}', ['uses' => 'ProdSupport\PsReportController@detailMoreInTable']);
    Route::get('reporting/find_by_date/{date}', ['uses' => 'ProdSupport\PsReportController@listDataByFilterDateApply']);
    Route::post('reporting/download', ['uses' => 'ProdSupport\PsReportController@downloadReport']);
});

Route::get('/find/check-user-activity',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getUserActivityInfo',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

// get ep error message
Route::get('/find/ep/error',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getEpErrorMessage',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

Route::post('/find/ep/error',  [
    'middleware' => ['auth', 'roles'],
    'uses' => 'EpController@getEpErrorMessage',
    'roles' => Config::get('constant.roles_adv_ep'),
]);

/*** Start App for It Support ***/
Route::group([
    'prefix' => '/it_support/',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_it_support'))
], function () {
    Route::get('data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@data_lookup']);
    Route::post('data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@data_lookup']);
    Route::post('add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@action_data_lookup']);
    Route::get('delete-data-lookup/{id}', ['uses' => 'ItSupport\ItChecklistEpController@delete_data_lookup']);
    Route::get('checklist', ['uses' => 'ItSupport\ItChecklistEpController@statusChecklist']);
    Route::post('checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateChecklist']);
    Route::get('find_by/{group}/{data}/{shift}', ['uses' => 'ItSupport\ItChecklistEpController@searchByGroupChecklist']);
    Route::get('checklist/remarks/helangdcstatus/{group}/{date}/{shift}', ['uses' => 'ItSupport\ItChecklistEpController@remarksStatus']);
    Route::get('checklist/remarks/status/{id}', ['uses' => 'ItSupport\ItChecklistEpController@remarksStatusDetails']);
    Route::get('checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@statusChecklistHistory']);
    Route::post('checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@updateChecklist']);
    Route::get('status/checklist/history/getmonths/{year}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getMonthList']);
    Route::get('status/checklist/history/lists/{year}/{month}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getListAllData']);
    Route::get('status/checklist/history/{date}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@byDateStatusChecklist']);
    Route::get('summary_details', ['uses' => 'ItSupport\ItChecklistEpController@summaryDetails']);
    Route::post('summary_details', ['uses' => 'ItSupport\ItChecklistEpController@summaryDetails']);
    Route::post('summary_details/create', ['uses' => 'ItSupport\ItChecklistEpController@actionSummaryDetails']);
    Route::get('server/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@server_data_lookup']);
    Route::post('server/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@server_data_lookup']);
    Route::post('server/add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@server_action_data_lookup']);
    Route::get('server/find_by/{group}/{data}/{shift}/{location}', ['uses' => 'ItSupport\ItChecklistEpController@searchByServerGroup']);
    Route::get('server/checklist', ['uses' => 'ItSupport\ItChecklistEpController@statusServerChecklist']);
    Route::post('server/checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateServerChecklist']);
    Route::get('server/checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@statusChecklistServerHistory']);
    Route::get('server/checklist/history/getmonths/{year}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getMonthList']);
    Route::get('server/checklist/history/lists/{year}/{month}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getListAllData']);
    Route::get('server/checklist/history/{date}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@byDateServerChecklist']);
    Route::match(['get', 'post'], 'server/checklist/history/date', ['uses' => 'ItSupport\ItChecklistEpController@statusChecklistServerHistoryDate'])
     ->name('statusChecklistServerHistoryDate');
    Route::get('dba_morning/checklist', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningChecklist']);
    Route::get('dba_morning/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningDataLookup']);
    Route::post('dba_morning/add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@dba_morning_data_lookup']);
    Route::get('dba_morning/find_by/{group}/{date}', ['uses' => 'ItSupport\ItChecklistEpController@searchDbaMorningByGroupChecklist']);
    Route::post('dba_morning/checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateDbaMorningChecklist']);
    Route::get('dba_morning/note/create', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningChecklist']);
    Route::get('dba_morning/checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningChecklistHistory']);
    Route::get('dba_morning/checklist/history/getmonths/{year}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getMonthList']);
    Route::get('dba_morning/checklist/history/lists/{year}/{month}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getListAllData']);
    Route::get('dba_morning/checklist/history/{date}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@byDateDbaChecklist']);
    Route::get('dba_morning/checklist/notes/history/{date}', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningChecklistNotesHistory']);
    Route::post('dba_morning/checklist/history/{date}', ['uses' => 'ItSupport\ItChecklistEpController@updateDbaMorningChecklist']);
    // Route::get('dba_morning/note/create/history', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningChecklistHistory']);
    Route::get('dba_morning/delete-data-lookup/{id}', ['uses' => 'ItSupport\ItChecklistEpController@dba_morning_delete_data_lookup']);
    Route::post('acknowledgement/status/note/create', ['uses' => 'ItSupport\ItChecklistEpController@dbaMorningCreateNote'])->name('dbaMorningCreateNote');
    Route::get('network/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@networkDataLookup']);
    Route::post('network/add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@add_network_data_lookup']);
    Route::get('network/performance/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@networkPerformanceDataLookup']);
    Route::post('network/performance/add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@addNetworkPerformanceDataLookup']);
    Route::get('network/amtek/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@networkAmtekDataLookup']);
    Route::post('network/amtek/add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@add_networkAmtek_data_lookup']);
    Route::get('network/checklist', ['uses' => 'ItSupport\ItChecklistEpController@networkChecklist']);
    Route::get('network/performance/checklist', ['uses' => 'ItSupport\ItChecklistEpController@networkPerformanceChecklist']);
    Route::get('network/amtek/checklist', ['uses' => 'ItSupport\ItChecklistEpController@networkAmtekChecklist']);
    Route::post('network/checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkChecklist']);
    Route::post('network/performance/checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkChecklist']);
    Route::post('network/amtek/checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkChecklist']);
    Route::get('network/status/find_by/{Location}/{group}/{date}', ['uses' => 'ItSupport\ItChecklistEpController@searchNetworkGroupChecklist']);
    Route::get('network/amtek/status/find_by/{Location}/{group}/{date}', ['uses' => 'ItSupport\ItChecklistEpController@searchNetworkGroupChecklist']);
    Route::get('network/performance/status/find_by/{Location}/{group}/{date}', ['uses' => 'ItSupport\ItChecklistEpController@searchNetworkGroupChecklist']);
    Route::get('network/checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@networkChecklistHistory']);
    Route::get('network/performance/checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@networkPerformanceChecklistHistory']);
    Route::get('network/amtek/checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@networkAmtekChecklistHistory']);
    Route::get('network/checklist/history/{date}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@byDateNetworkChecklist']);
    Route::get('network/checklist/history/getmonths/{year}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getMonthList']);
    Route::get('network/checklist/history/lists/{year}/{month}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@getListAllData']);
    Route::get('network/checklist/notes/history/{date}/{section}', ['uses' => 'ItSupport\ItChecklistEpController@networkChecklistNotesHistory']);
    Route::post('network/checklist/history/{date}', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkChecklist']);
    Route::post('network/performance/checklist/history/{date}', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkChecklist']);
    Route::post('network/amtek/checklist/history/{date}', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkChecklist']); 

    Route::get('network/backup/data_lookup', ['uses' => 'ItSupport\ItChecklistEpController@networkBackupLookup']);
    Route::post('network/backup/add_data_lookup/create', ['uses' => 'ItSupport\ItChecklistEpController@add_networkBackup_data_lookup']);
    Route::get('network/backup/checklist', ['uses' => 'ItSupport\ItChecklistEpController@networkBackupList'])->defaults('location', 'cyber');
    Route::post('network/backup/checklist', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkBackupList']);
    Route::get('network/backup/checklist/wisma', ['uses' => 'ItSupport\ItChecklistEpController@networkBackupList'])->defaults('location', 'wisma');
    Route::post('network/backup/checklist/wisma', ['uses' => 'ItSupport\ItChecklistEpController@updateNetworkBackupList']);
    Route::get('network/backup/checklist/history', ['uses' => 'ItSupport\ItChecklistEpController@networkBackupChecklistHistory']);
    Route::get('network/backup/checklist/history/wisma', ['uses' => 'ItSupport\ItChecklistEpController@networkBackupWismaChecklistHistory']);
});
    

/*** Monitoring eP / BPM for DBA Team ***/
Route::group([
    'prefix' => '/dba-support/',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_dba_users'))
], function () {
    Route::get('report/CompositeInstanceMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showCompositeInstanceMonitoringReport']);
    Route::post('report/CompositeInstanceMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showCompositeInstanceMonitoringReport']);
    Route::get('report/CubeInstMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showCubeInstanceMonitoringReport']);
    Route::post('report/CubeInstMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showCubeInstanceMonitoringReport']);
    Route::get('report/WFTaskMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showWFTaskMonitoringReport']);
    Route::post('report/WFTaskMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showWFTaskMonitoringReport']);
    Route::get('report/SCAFlowInstMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showSCAFlowInstMonitoringReport']);
    Route::post('report/SCAFlowInstMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showSCAFlowInstMonitoringReport']);
    Route::get('report/DLVMsgMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showDLVMsgMonitoringReport']);
    Route::post('report/DLVMsgMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showDLVMsgMonitoringReport']);
    Route::get('report/TableGrowthMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showTableGrowthMonitoringReport']);
    Route::post('report/TableGrowthMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showTableGrowthMonitoringReport']);
    Route::get('report/AppDataGrowthMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showAppDataGrowthMonitoringReport']);
    Route::post('report/AppDataGrowthMonitoringReport', ['uses' => 'DBASupport\MonitorPurgingController@showAppDataGrowthMonitoringReport']);
    
});

/* Start App for Logtrace 
 * To manage user activity log 
 */
Route::group([
    'prefix' => 'log',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation'))
], function () {
    Route::get('/', ['uses' => 'LogTrace\TraceLogController@findDefaultLog']);
    Route::post('/', ['uses' => 'LogTrace\TraceLogController@findLogByCriteria']);
    Route::get('/info', ['uses' => 'LogTrace\TraceLogController@findLogById']);
    Route::get('/clear', ['uses' => 'LogTrace\TraceLogController@clearLogTrace']);
    Route::get('/login-history', ['uses' => 'LogTrace\TraceLogController@findDefaultLoginHistory']);
    Route::post('/login-history', ['uses' => 'LogTrace\TraceLogController@findLoginHistoryByCriteria']);
    Route::group([
        'prefix' => 'dashboard',
        'middleware' => ['auth', 'roles'],
        'roles' => Config::get('constant.roles_adv_ep')
    ], function () {
        Route::get('/', ['uses' => 'LogTrace\TraceLogController@openDashboardMonitoring']);
        Route::get('/internal', ['uses' => 'LogTrace\TraceLogController@openiDashboardMonitoring']);
    });
    Route::group([
        'prefix' => 'patch',
        'middleware' => ['auth', 'roles'],
        'roles' => Config::get('constant.roles_adv_ep')
    ], function () {
        Route::get('/', ['uses' => 'LogTrace\TraceLogController@openPatchDashboard']);
        Route::get('/exec', ['uses' => 'LogTrace\TraceLogController@fixTmpLogFileFail']);
        Route::get('/fail', ['uses' => 'LogTrace\TraceLogController@getTmpLogFileFailToProcess']);
        Route::get('/error', ['uses' => 'LogTrace\TraceLogController@grepTmpLogErrorMessage']);
        Route::get('/empty-content-log-error', ['uses' => 'LogTrace\TraceLogController@emptyTmpLogErrorContent']);
        Route::get('/grep', ['uses' => 'LogTrace\TraceLogController@shipLogToTmpLogFile']);
    });
    Route::group([
        'prefix' => 'daily',
        'middleware' => ['auth', 'roles'],
        'roles' => Config::get('constant.roles_adv_ep')
    ], function () {
        Route::get('/by/user', ['uses' => 'LogTrace\TraceLogController@dailyAccessByUser']);
        Route::get('/by/node', ['uses' => 'LogTrace\TraceLogController@dailyRequestByEnvAndServerNode']);
        Route::get('/by/furl', ['uses' => 'LogTrace\TraceLogController@dailyLogTraceGrpByFriendlyUrl']);
    });
    Route::group([
        'prefix' => 'job',
        'middleware' => ['auth', 'roles'],
        'roles' => Config::get('constant.roles_adv_ep')
    ], function () {
        Route::get('/run', ['uses' => 'LogTrace\TraceLogController@runJob']);
    });
});

/* Start App for SPKI Support Tasks 
 * To manage spki tasks for prod support
 */
Route::group([
    'prefix' => 'support-spki',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_it_support'))
], function () {
    Route::match(['get', 'post', 'put'], 'task', 'EpSupportController@listSpkiTask');
    Route::get('task/save', 'EpSupportController@saveTaskSpki');
    Route::get('task/detail/{taskID}', 'EpSupportController@getTaskSpki');
    Route::get('task/list/{carian}', 'EpSupportController@searchEpTaskSpki');
    Route::match(['get', 'post', 'put'], 'task/list', 'EpSupportController@searchEpTaskListSpki');
});

/*
 * HELPDESK MENU
 */
Route::group([
    'prefix' => 'helpdesk',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.users_helpdesk'))
], function () {
    Route::match(['get', 'post', 'put'], 'ticket', 'Helpdesk\TicketController@checkTicket');
    Route::post('updateticket/{ticketId}', 'Helpdesk\TicketController@updateTicket');
    Route::post('updateticketsubject/{ticketId}', 'Helpdesk\TicketController@updateTicketSubject');
    Route::post('addentry/{ticketId}', 'Helpdesk\TicketController@addThreadEntry');
    Route::post('updatethreadentry/{entryId}', 'Helpdesk\TicketController@updateThreadEntry');
    Route::post('deleteThreadEntry', 'Helpdesk\TicketController@deleteThreadEntry');
    Route::post('deleteThreadEvent', 'Helpdesk\TicketController@deleteThreadEvent');
});

/*
 * EP NOTIFY MENU
 */
Route::group([
    'prefix' => 'wsnotify',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_specialist'), Config::get('constant.roles_it_specialist'))
], function () {
    Route::match(['get', 'post', 'put'], 'dashboard/{type}', 'EpNotify\EpNotifyController@dashboard');
    Route::match(['get', 'post', 'put'], 'reminder/{type}', 'EpNotify\EpNotifyController@reminder');
    Route::match(['get', 'post', 'put'], 'receiver/{type}', 'EpNotify\EpNotifyController@receiver');
    Route::match(['get', 'post', 'put'], 'receivergroup/{type}', 'EpNotify\EpNotifyController@receiverGroup');

    Route::get('total-pending-notifications/{type}', 'EpNotify\EpNotifyController@totalPendingNotifications');
    Route::get('detail-pending-notifications/{type}', 'EpNotify\EpNotifyController@detailPendingNotifications');
});

/*
 * CRM MANAGEMENT MENU
 */
Route::group([
    'prefix' => 'crmmanagement',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_middleware'))
], function () {
    Route::match(['get', 'post', 'put'], 'main', 'CrmManagement\CrmManagementController@main');
    Route::post('updatecrm', 'CrmManagement\CrmManagementController@updateCrm');
    Route::post('getList', 'CrmManagement\CrmManagementController@getList');
    Route::post('deactivate-user', 'CrmManagement\CrmManagementController@deactivateUser');
});

Route::group([
    'prefix' => 'report',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_middleware'))
], function () {
    Route::match(['get', 'post',], 'perubahandata', 'CrmDashboard\ReportCrmController@reportPerubahanData');
    Route::match(['get',], 'cptpp', 'CrmDashboard\ReportCrmController@reportCptpp');
    Route::match(['get',], 'pemantauan_cptpp_qt', 'CrmDashboard\ReportCrmController@pemantauanCptppQT');
});


/*
 * TECH REFRESH ISSUE MENU
 */
Route::group([
    'prefix' => 'techrefresh',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'),Config::get('constant.roles_it_developer'))
], function () {
    Route::match(['get', 'post', 'put'], 'monitoring', 'AppSupport\TechRefreshIssueController@getMonitoringList');
});

/*
 * EP SUPPORT ACTION LOG MENU
 */
Route::group([
    'prefix' => 'actionlog',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'),Config::get('constant.roles_it_developer'))
], function () {
    Route::match(['get', 'post', 'put'], 'main', 'EpSupportActionLogController@main');
    Route::match(['get'], 'fetch', 'EpSupportActionLogController@fetch');
    Route::post('search', 'EpSupportActionLogController@search');
});

/*
 * EP blast email
 */
Route::group([
    'prefix' => '/ep/blast/email',
    // 'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_ep_specialist'))
], function () {
    Route::get('/front', ['uses' => 'EpController@ePBlastSummary']);
    Route::get('/error/message', ['uses' => 'EpController@ePBlastErrorMessageByDate']);
    Route::post('/error/message', ['uses' => 'EpController@ePBlastErrorMessageByDate']);
    Route::get('/list/campaign/completed', ['uses' => 'EpController@ePBlastListCampaignCompleted']);
    Route::get('/list/campaign/pending', ['uses' => 'EpController@ePBlastListCampaignPending']);
});


/*
 CT Cancel Agreement
 */
Route::group([
    'prefix' => '/ep/ct',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'),Config::get('constant.roles_it_developer'))
], function () {
    Route::get('/stat-ct-cancel-batch-no', ['uses' => 'AppSupport\CtCancelAgreementController@listCtCancelAgreementStatBatchNo']);
    Route::get('/list-ct-cancel-agreement', ['uses' => 'AppSupport\CtCancelAgreementController@listCtCancelAgreement']);
    Route::get('/list-ct-agreement-null', ['uses' => 'AppSupport\CtCancelAgreementController@listCtAgreementNull']);
    Route::get('/list-ct-pending-amendment', ['uses' => 'AppSupport\CtCancelAgreementController@listCtPendingAmendment']);
    Route::get('/list-ct-agreement-not-null', ['uses' => 'AppSupport\CtCancelAgreementController@listCtAgreementNotNull']);
    Route::get('/list-ct-agreement-missing', ['uses' => 'AppSupport\CtCancelAgreementController@listCtAgreementMissing']);
    
});


/* POMS MENU */
Route::group([
    'prefix' => 'poms',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.users_poms'))
], function () {
    Route::get('/incident', 'POMSCRMDashboardController@main')->name('poms.dashboard');
    Route::get('/case-list', 'POMSCRMDashboardController@getCaseList')->name('poms.case.list');
    Route::post('/crm/modal-data', 'POMSCRMDashboardController@getModalData')->name('poms.crm.modal.data');
    Route::get('/check/system-availability', ['uses' => 'Poms\PomsController@checkSystemAvailability']);
    Route::post('/calculate-sla', ['uses' => 'Poms\PomsController@calculateSlaApi']);
    Route::get('fix/incident-tasks', 'Poms\PomsController@incidentTasks')->name('incident.tasks');
    Route::post('fix/incident-tasks/update', 'Poms\PomsController@updateTask')->name('incident.tasks.update');
    Route::post('fix/sla-summary/update', 'Poms\PomsController@updateSlaSummary')->name('sla.summary.update');
    Route::post('fix/fetch-sla-summary', 'Poms\PomsController@fetchSlaSummary')->name('sla.summary.fetch');
});

/** AP511 Check Invoice Module **/
Route::group([
    'prefix' => 'ap511-check-invoice',
    'middleware' => ['auth', 'roles'],
    'roles' => array_merge(Config::get('constant.roles_adv_ep'), Config::get('constant.roles_ep_operation'))
], function () {
    Route::get('/dashboard', 'Ap511InvoiceController@dashboard')->name('ap511.invoice.dashboard');
    Route::get('/list', 'Ap511InvoiceController@invoiceList')->name('ap511.invoice.list');
    Route::get('/data', 'Ap511InvoiceController@getInvoiceData')->name('ap511.invoice.data');
    Route::get('/dashboard-data', 'Ap511InvoiceController@getDashboardData')->name('ap511.invoice.dashboard.data');
    Route::post('/export', 'Ap511InvoiceController@exportExcel')->name('ap511.invoice.export');
    Route::get('/sync', 'Ap511InvoiceController@sync')->name('ap511.invoice.sync');
    Route::post('/sync/start', 'Ap511InvoiceController@startSync')->name('ap511.invoice.sync.start');
    Route::post('/sync/stop', 'Ap511InvoiceController@stopSync')->name('ap511.invoice.sync.stop');
    Route::get('/sync/progress', 'Ap511InvoiceController@getSyncProgress')->name('ap511.invoice.sync.progress');
    Route::get('/sync/log', 'Ap511InvoiceController@downloadSyncLog')->name('ap511.invoice.sync.log');
});