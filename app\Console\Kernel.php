<?php

namespace App\Console;

use Illuminate\Console\Command;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Log;
use Carbon\Carbon;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [

        /** Process Activity Scheduler to support eP Integration / Stuck Task BPM */
        Commands\HandleGFM100ErrorSchedule::class,
        Commands\HandleOSBServiceRetry::class,
        Commands\MonitorQTSchedule::class,
        Commands\HandleGFM120ErrorSchedule::class,
        Commands\HandleGFM020ErrorSchedule::class,
        Commands\HandleLoginStatistic::class,
        Commands\HandleGFM090APERRErrorSchedule::class,
        Commands\HandlePaymentClosedAP511Schedule::class,
        Commands\HandleEPP013ErrorSchedule::class,
        Commands\HandleIgfmasFileTriggerSchedule::class,
        Commands\HandleNotifyMessageBulkQT::class,
        Commands\HandlePocoClosedButMissingPendingPaymentSchedule::class,
        Commands\HandleMyGpisStatisticSchedule::class,
        Commands\HandleAlertMyGpisMonitoringSchedule::class,
        Commands\HandleAlertEgpaMonitoringSchedule::class,
        Commands\HandleAlertBatchOutboundFileDailySchedule::class,
        Commands\HandleQTServiceRetryInvalidStatus::class,
        Commands\HandleRemoveTask::class,
        Commands\HandleIgfmasFileAP511CreationTransIDSchedule::class,
        Commands\HandleRestartTomcat::class,
        Commands\HandleIgfmasInvoiceAP511NotUpdatedInEp::class,
        Commands\HandleOutboundFileStuckFileSchedule::class,
        Commands\HandlePaymentReceiptAR502Schedule::class,
        Commands\HandleTerminateCodiTaskNotificationExpiredSchedule::class,
        Commands\MonitorSsmOsbService::class,
        Commands\HandleMminfSpecialCharSchedule::class,
        Commands\HandleMminfItemNotExistSchedule::class,

        /** Process Activity Scheduler Features Prod Support Team */
        Commands\ProdSupport\HandleEtlRptStatTransactEp::class,
        Commands\ProdSupport\HandleRefreshDefectEpTesting::class,
        Commands\ProdSupport\HandleSeverityCases::class,
        Commands\ProdSupport\HandleSlaRitCases::class,
        Commands\ProdSupport\HandleRefreshTempTableCptpp::class,
        Commands\ProdSupport\HandleReportSmMofStatistic::class,

        /** Process Activity Scheduler to support eP App application */
        Commands\AppSupport\HandleSmSoftcertFixRenewal::class,
        Commands\AppSupport\HandleSmSiteVisitTaskDuplicateData::class,
        Commands\AppSupport\HandleSmRoleDuplicateRoleSameUserOrgId::class,
        Commands\AppSupport\HandleDisableSmsNotifyUser::class,
        Commands\AppSupport\HandleReportVerifyDisciplinaryAction::class,
        Commands\AppSupport\HandleSmFixIssueCancellationBumiStatus::class,
        Commands\AppSupport\HandleTimeShiftPaymentMolpayPaid::class,
        Commands\AppSupport\HandleDuplicateRoleSmProfileUser::class,
        Commands\AppSupport\HandleReportPaymentMolpay::class,
        Commands\AppSupport\HandleReportActionEpAdmin::class,
        Commands\AppSupport\HandleAlertPaymentReceiptAR502::class,
        Commands\AppSupport\HandleSmPaymentProcessFixStuck::class,
        Commands\AppSupport\HandleSmPaymentCardTypeIssueRazer::class,
        Commands\AppSupport\HandleSmSoftcertFreeResetToPay::class,
        Commands\AppSupport\HandleSmSupplierUpdateChangedDateToToday::class,

        /** Process Activity Scheduler Features IT Support Team */
        Commands\ItSupport\HandleInsertNewTaskEverDay::class,
        Commands\ItSupport\HandleInsertNewTaskServerEverDay::class,
        Commands\ItSupport\HandleInsertNewTaskDbaMorningEverDay::class,
        Commands\ItSupport\HandleInsertNewTaskNetworkEveryDay::class,
        Commands\ItSupport\HandleInsertNewTaskNetworkPerformEveryDay::class,
        Commands\ItSupport\HandleInsertNewTaskNetworkAmtekEveryDay::class,
        Commands\ItSupport\HandleInsertNewTaskNetworkBackup::class,

        /** Log Trace */
        Commands\LogTrace\HandleLogTraceDailyRequestByUser::class,
        Commands\LogTrace\LogTraceCubeJob::class,
        Commands\LogTrace\ClearStuckLogTraceFile::class,
        Commands\LogTrace\ClearStuckLogTraceFilePartial::class,
        Commands\LogTrace\ExtractBacklogTraceToFactTable::class,
        Commands\LogTrace\LogTraceMonitoringAlertBackLogFile::class,

        /** SPKI Service Tracker */
        Commands\SPKI\LogSpkiAvailabilitySchedule::class,
        Commands\SPKI\HandleFailedSpkiRequestCertificaticeByOSB::class,

        
        /** Monitoring SLA PMO */
        Commands\ProdSupport\HandleSlaPmo::class,
        Commands\ProdSupport\HandleSlaPmoAssessment::class,
        
        Commands\MonitorBpmStlRunning::class,

        Commands\MonitorCtAgreement::class,

        /** RUN ONE TIME TO CLEAR STUCK VERIFY */
        Commands\HandleStuckVerifyLoa::class,

        Commands\GoogleAnalyticsEP\ImageGeneratorVisitorCountEPSchedule::class,

        /** INTEGRATION PROGRAM  */
        Commands\Integration\HandleProcessIntegrationIsCurrentSchedule::class,
        Commands\Integration\HandleErrorTransactionFileAPERRSchedule::class,

        Commands\Baileys\HandleBaileysStatusMonitoring::class,
        Commands\CasePerubahanDataSchedule::class,

        Commands\HandleMonitorQTSpecCount::class,
        Commands\HandleQTMonitoringPublishingClosing::class,
        Commands\HandleCaptureQtAcceptHistory::class,

        Commands\HandleCasesMonitoringBPK::class,

        Commands\HandleBPMQTMaintenance::class,
        Commands\HandleBPMQTMaintenanceStatusReport::class,
        Commands\HandleCheckGPKIMedium::class,
        Commands\HandleUpdateEpGPKISigningListStatusUserOrg::class,
        Commands\HandleBpmMissingEvaluation::class,
        Commands\Ap511SyncBackgroundCommand::class,

    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Restart TOMCAT every morning 6:33am, Disabled.
        // $schedule->command('HandleRestartTomcat')->dailyAt("04:33");
        // $schedule->command('HandleRestartTomcat')->dailyAt("09:51");
        
        /**
         * Whatapp notify. Start 26/6/2025. Set Disable 30/6/2025
         * Fix this issue on 30/6/2025 ->reupdate version puppeteer 1.31.0
         **/
        // $schedule->command('ws-notify-auto-fix-error')->everyMinute();

        
        //Off on 12am until 2AM. schedule backup eP Database
        $schedule->command('set-notify-sms-disabled')->everyFiveMinutes()->between('02:00', '23:55');

        $schedule->command('monitor-qt')->everyFiveMinutes()->between('02:00', '11:55');
        $schedule->command('monitor-qt')->everyMinute()->between('11:56', '11:59');
        $schedule->command('ep_login_statistic')->hourly();

        //$schedule->command('HandleGFM100Error')->hourlyAt('23');
        //$schedule->command('HandleGFM100Error')->hourlyAt('57');
        $schedule->command('HandleGFM100Error')->everyFiveMinutes(); // Kod Barang tidak wujud || Unit ukuran tidak wujud

        $schedule->command('HandleGFM120Error')->hourly();
        $schedule->command('HandleGFM020Error')->daily();
        $schedule->command('HandleGFM090APERRError')->hourly(); // // SEND APIVE issue APERR receive : currently blocked
        $schedule->command('HandleErrorTransactionFileAPERR')->dailyAt("23:56"); // SEND APIVE issue APERR receive : Kod Pembekal masih mempunyai transaksi di iGFMAS
        $schedule->command('HandleMminfSpecialChar')->everyMinute();
        
        /** 
         * To handle issue code item not insert in DI_MMINF. Call OSB Service to changed date SC_REQUEST_ITEM.
         * Exclude 1pm to 1:59pm
        **/
        $schedule->command('HandleMminfItemNotExist')->everyFiveMinutes();
        //$schedule->command('HandleMminfItemNotExist')->hourlyAt('2');
        //$schedule->command('HandleMminfItemNotExist')->hourlyAt('33');

        //Decrypt file contents for service GFM-140 AP511 to put temporary table
        $schedule->command('HandlePaymentClosedAP511')->hourlyAt('14');
        // extract info receipt_no from AR502 file to ep_supplier_ar502
        $schedule->command('HandlePaymentReceiptAR502')->hourlyAt('13');
        $schedule->command('HandlePaymentReceiptAR502')->everyMinute()->between('06:00', '06:29');

        $schedule->command('error-epp013-trigger')->twiceDaily(6, 19);
        $schedule->command('error-epp013-trigger')->twiceDaily(11, 15);

        /** Run the task every hour at 3 mins past the hour **/
        //$schedule->command('igfmas-file-trigger')->hourlyAt(3);
        $schedule->command('igfmas-file-trigger')->hourlyAt(53);

        //$schedule->command('fix-notify-message-bulk-qt')->everyFiveMinutes();

        $schedule->command('sm-sync-create-user-ssoliferay')->cron('*/6 * * * *');
        $schedule->command('sm-sync-pending-process')->everyFiveMinutes();

        /** This script will run earlier before PLSQL script run on 2PM and 9PM */
        $schedule->command('HandlePocoClosedButMissingPendingPaymentSchedule')->dailyAt("13:51");
        $schedule->command('HandlePocoClosedButMissingPendingPaymentSchedule')->twiceDaily(6, 19);
        $schedule->command('HandlePocoClosedButMissingPendingPaymentSchedule')->dailyAt("20:51");

        $schedule->command('HandleTerminateCodiTaskNotificationExpiredSchedule')->dailyAt("07:33");

        /** MyGPIS:  save statistic daily record extract sent to MyGPIS*/
        $schedule->command('HandleMyGpisStatistic')->hourly();

        /** MyGPIS: Monitoring StoreProcedure execution for MYGPIS MODULE EXCLUDE IKLAN at 12AM (Daily) and 7AM (Saturday). The taken time must not exceed on 2AM or 9AM. If exceed, Alert WhatApp, support operation need manually action */
        $schedule->command('HandleAlertMyGpisMonitoring SESSION_MORNING 2')->weekdays()->at("02:01");
        $schedule->command('HandleAlertMyGpisMonitoring SESSION_MORNING 9')->saturdays()->at("09:01");
        $schedule->command('HandleAlertMyGpisMonitoring SESSION_MORNING 2')->sundays()->at("02:01");

        /** MyGPIS: Monitoring StoreProcedure execution for MYGPIS_IKLAN at 5PM. Set 18:00 to compare execution time not exceed after 6PM. If exceed, Alert WhatApp,  support operation need manually action*/
        $schedule->command('HandleAlertMyGpisMonitoring SESSION_EVENING 18')->dailyAt("18:01");
        

        /** eGPA: Monitoring daily scheduler. If not succesfully run will trigger notification whatapp*/
        $schedule->command('HandleAlertEgpaMonitoring')->dailyAt("01:49");
        $schedule->command('HandleAlertEgpaMonitoring')->dailyAt("08:51");


        $schedule->command('HandleAlertBatchOutboundFileDaily')->dailyAt("02:06"); //Check ePOL & EGPA files generated or not
        $schedule->command('HandleAlertBatchOutboundFileDaily')->dailyAt("03:06"); //Check MyGPIS files generated or not
        $schedule->command('HandleAlertBatchOutboundFileDaily')->dailyAt("06:06"); //Check IGFMAS files generated or not
        $schedule->command('HandleAlertBatchOutboundFileDaily')->dailyAt("10:01"); //Check files for saturday
        
        /*** Run the task every hour at 11 or 41 mins past the hour : This will check file in OUT folder to call OSB pickup file then send file to Integration Third Party Server
         * Integration :MyGPIS, LMS, PHS
        **/
        $schedule->command('ep-out-trigger-file-stuck')->hourlyAt(6);
        $schedule->command('ep-out-trigger-file-stuck')->hourlyAt(36);

        /** delete record in OSB_BATCH_RETRY_DTL , if any records already succeed transferred **/
        $schedule->command('osb-service-retry-fix')->hourly();
        //$schedule->command('igfmas-ap511-trigger-trans-id')->hourly();
        $schedule->command('igfmas-ap511-trigger-trans-id')->everyFiveMinutes();

        $schedule->command('igfmas-ap511-update-payment-closed')->hourly();


        $schedule->command('ps-etl-rpt-stat-transact')->monthlyOn(1, "05:30");

        /**
         * TO reupdate process_status = 1 for QT in invalid status
         */
        $schedule->command('qt-service-retry-invalid-status')->dailyAt("07:30");

        /**
         * TO update record_status = 8
         */
        $schedule->command('patch-inprogress-remove-task')->everyTenMinutes();
        
        /**
         * To remove duplicate data in SM Site-visit Task Table: SM_SV_TASK
         */
        $schedule->command('sm-remove-duplicate-site-visit-task')->dailyAt("06:35");

        /**
         * To refresh data from redmine
         */
        $schedule->command('ps-refresh-defect-ep-redmine')->everyTenMinutes();
        
        $schedule->command('HandleSeverityCases')->hourly();
        $schedule->command('HandleSlaRitCases')->hourly();

        /**
         * Send report monthly for eP UPS
         * #0000008369 | PERMOHONAN DATA BULANAN-crawling message UPS
         */
        $schedule->command('HandleReportSmMofStatistic')->monthlyOn(1, "10:01");


        /**
         * Send report for monthly ePO for tindakan eP Admin
         */
        $schedule->command('HandleReportActionEpAdmin')->monthlyOn(1, "10:06");
        
        /**
         * Issue cause of payment date in PY_PAYMENT is not same with date paid transaction from MOLPAY
         */
        $schedule->command('HandleTimeShiftPaymentMolpayPaid')->dailyAt("18:31");
        $schedule->command('HandleTimeShiftPaymentMolpayPaid')->dailyAt("23:56");
        $schedule->command('HandleTimeShiftPaymentMolpayPaid')->dailyAt("05:31");
        $schedule->command('HandleReportPaymentMolpay')->hourlyAt("1");
        $schedule->command('HandleReportPaymentMolpay')->hourlyAt("58");
        $schedule->command('HandleAlertPaymentReceiptAR502')->dailyAt("00:04");
        $schedule->command('HandleAlertPaymentReceiptAR502')->dailyAt("05:51");
        $schedule->command('HandleAlertPaymentReceiptAR502')->dailyAt("06:04");

        $schedule->command('ep-receipt-sync '.Carbon::now()->format('Y-m-d'))->hourlyAt("59");

        /**
         * 'To fix issue on channel_name for CREDIT / DEBIT cause of razer get the different info card with not same with maybank'
         */
        $schedule->command('HandleSmPaymentCardTypeIssueRazer')->hourlyAt("27");
        $schedule->command('HandleSmPaymentCardTypeIssueRazer')->hourlyAt("57");
        /**
         * 
         * To update is_softcert field in SM_PERSONNEL change status 7 to 1. Softcert still not expired yet. Will ignored if supplier already has task pending payment softcert
         */
        $schedule->command('HandleSmSoftcertFixRenewal')->dailyAt("09:10");
        $schedule->command('HandleSmSoftcertFixRenewal')->dailyAt("10:10");
        $schedule->command('HandleSmSoftcertFixRenewal')->dailyAt("11:10");


        /**
         * 
         * Handle issue supplier active still not exist sap_vendor_code. Reupdate changed_date to resend apive 
         * Skip on 12am,12pm and 1pm
         */
        
        //$schedule->command('HandleSmSupplierUpdateChangedDateToToday')->everyFiveMinutes()->when(function () {
        //     $currentHour = Carbon::now()->hour;
        //     return $currentHour != 0 || $currentHour != 12 || $currentHour != 13; // 1 PM is represented as 13 in 24-hour format 
        //});
        $schedule->command('HandleSmSupplierUpdateChangedDateToToday')->dailyAt("08:16");

        /**
         * TO reupdate record_status frpm 0 to 9 for duplicate roles.
         */
        $schedule->command('sm-role-user-duplicate')->dailyAt("21:36");
        //$schedule->command('handle-duplicate-role-sm-user')->twiceDaily(7, 21);
        $schedule->command('handle-duplicate-role-sm-user')->dailyAt("08:03");
        $schedule->command('sm-role-user-duplicate')->dailyAt("07:01");


        /**
         * To send report by email list of disciplinary action supplier on activity application SM and proposal QT
         */
        $schedule->command('report-mail-disciplinary-action-supplier')->twiceDaily(9, 14);


        /**
         * IT Support - Task . To do checklist eP Status. This program will create task daily
         */
        $schedule->command('ItS-insert-new-task')->dailyAt("00:01");
        $schedule->command('ItServer-insert-new-task')->dailyAt("00:01");
        $schedule->command('Dba-Morning-Checklist-insert-new-task')->dailyAt("00:01");
        $schedule->command('Network-Checklist-insert-new-task')->dailyAt("00:01");
        $schedule->command('Network-Performance-Checklist-insert-new-task')->dailyAt("00:01");
        $schedule->command('Network-Amtek-insert-new-task')->dailyAt("00:01");
        $schedule->command('ps-refresh-temp-table-cptpp')->dailyAt("10:00");
        $schedule->command('Network-backup-insert-new-task')->dailyAt("00:01");

        /*
        * Log Trace - scheduler to run daily report for total request by user and by node
        */
        $schedule->command('HandleLogTraceDailyRequestByUser')->hourlyAt(59);
        $schedule->command('LogTraceCubeJob')->hourlyAt(59);
        $schedule->command('ClearStuckLogTraceFile')->dailyAt("01:11");
        //$schedule->command('ClearStuckLogTraceFile')->dailyAt("04:21");
        $schedule->command('ClearStuckLogTraceFilePartial')->everyMinute();
        $schedule->command('ExtractBacklogTraceToFactTable')->dailyAt("04:40");
        $schedule->command('LogTraceMonitoringAlertBackLogFile')->dailyAt("9:03");
        

        /*
        * SPKI Service - scheduler to run every 5 minutes to check service availability
        */
        $schedule->command('LogSpkiAvailabilitySchedule')->everyFiveMinutes();

        // set off 2/12/2023 ,no issue error on TG/DG..
        //$schedule->command('HandleFailedSpkiRequestCertificaticeByOSB')->hourlyAt(2);

        /*
        * SM PENDING PAYMENT - scheduler to run every hour to remove stuck invalid
        */
        $schedule->command('HandleSmPaymentProcessFixStuck')->hourlyAt(2);
        $schedule->command('HandleSmPaymentProcessFixStuck')->hourlyAt(17);
        $schedule->command('HandleSmPaymentProcessFixStuck')->hourlyAt(33);
        $schedule->command('HandleSmPaymentProcessFixStuck')->hourlyAt(48);

        //Deprecated - used for tech refresh
        //$schedule->command('insert-instance-old-version')->dailyAt("01:16");

        // $schedule->command('report-ep-supplier-igfmas')->dailyAt("20:59");

        /*
         * Handle issue supplier active but still not exist SAP VENDOR CODE
         */
        // $schedule->command('sm:supplier-update-changed-date')
        //     ->everyFiveMinutes()
        //     ->between('2:00', '12:00')
        //     ->between('14:00', '23:00');

        /*
         * SLA PMO FOR S4
         */
        $schedule->command('HandleSlaPmo')->dailyAt("10:00");
        $schedule->command('HandleSlaPmoAssessment')->dailyAt("11:00");
        
                /**
         * Monitor SSM OSB Service
         */
        $schedule->command('monitor-ssm-osb-service')->everyTenMinutes();


        
        /** TEMPORARY ISSUE TG FAILED, disabled on 08/09/2023 */
        //$schedule->command('resend-failed-tg-to-dg')->hourlyAt(1);
        //$schedule->command('resend-failed-tg-to-dg')->hourlyAt(16);
        //$schedule->command('resend-failed-tg-to-dg')->hourlyAt(31);
        //$schedule->command('resend-failed-tg-to-dg')->hourlyAt(46);
        
        /** MONITOR BPM STL WITH STATUS RUNNING */
        $schedule->command('monitor-bpm-stl-running')->dailyAt("09:00");
        $schedule->command('monitor-bpm-stl-running')->dailyAt("11:00");
        $schedule->command('monitor-bpm-stl-running')->dailyAt("14:00");
        $schedule->command('monitor-bpm-stl-running')->dailyAt("16:00");

        $schedule->command('qt-bpm-missing-evaluation')->everyTenMinutes()->between('12:15', '13:00');
        
        /** MONITOR BPM STL CT AGREEMENT */
        $schedule->command('monitor-ct-agreement')->everyThirtyMinutes();

        /** RUN ONE TIME TO CLEAR STUCK VERIFY */
        // $schedule->command('stuck-verify-loa')->fridays()->at("17:40");

        /** GENERATE eP VISITOR COUNT IMAGE - EVERY HOUR */
        $schedule->command('ep-visitor-count')->dailyAt("08:00");

        /** TO reset DI_INTERFACE_LOG process id file..set is_current only one record active */
        $schedule->command('HandleProcessIntegrationIsCurrent')->dailyAt("19:41");
        
        $schedule->command('case-perubahan-data-monthly')->monthlyOn(1, '07:50');

        $schedule->command('monitor-bpk-cases')->hourly();

        /**
         * Monitor quotations with high specification item count (more than 1000) and send notification
         * */
        $schedule->command('monitor-qt-spec-count')->dailyAt("14:00");

        $schedule->command('monitor-qt-publishing-closing')->dailyAt("11:00");

        $schedule->command('qt:capture-accept-history')->dailyAt("19:00");

        /**
         * Handle Baileys Status Monitoring
         */
        $schedule->command('whatsapp:monitor')->hourlyAt(5);

        /**
         * Handle Check GPKI Medium
         */
        // $schedule->command('gpki:check-medium')->withoutOverlapping()->hourly()->runInBackground();

        // off .. set using crontab in server
        // $schedule->command('osb-service-retry 2024-12-04 1GFMAS StopInstruction 25')->everyMinute();
        // schedule->command('osb-service-retry 2024-12-04 1GFMAS DebitAdviceNote 25')->everyMinute();
        // $schedule->command('osb-service-retry 2024-12-04 1GFMAS POContractForGoodsAndServices 25')->everyMinute();
        // schedule->command('osb-service-retry 2024-12-04 1GFMAS MasterDataMaterialInfo 25')->everyMinute();
        // $schedule->command('osb-service-retry 2024-12-04 1GFMAS FulfillmentReceivingNote 25')->everyMinute();
        // $schedule->command('osb-service-retry 2024-12-04 1GFMAS PaymentInstruction 25')->everyMinute();

        /**
         * To reset password user eP that already remind 45 days to manually change password.
         */
        $schedule->command('ep-sso-sync-reset-password 1000 PROCEED')->dailyAt("07:13");
        $schedule->command('ep-sso-sync-change-password-blast-user BATCH_NO_1 PROCEED')->dailyAt("08:13");
        $schedule->command('ep-sso-sync-change-password-blast-user BATCH_NO_1 PROCEED')->dailyAt("14:13");
        $schedule->command('ep-sso-sync-change-password-blast-user BATCH_NO_1 PROCEED')->dailyAt("23:13");
        $schedule->command('ep-sso-sync-change-password-blast-user BATCH_NO_2 PROCEED')->dailyAt("08:14");
        $schedule->command('ep-sso-sync-change-password-blast-user BATCH_NO_2 PROCEED')->dailyAt("14:14");
        $schedule->command('ep-sso-sync-change-password-blast-user BATCH_NO_2 PROCEED')->dailyAt("23:14");
        
        

    }
 
    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        require base_path('routes/console.php');
    }
}
