@extends('layouts.guest-dash')

@section('cssprivate')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
@if (Auth::user())
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="fa fa-refresh"></i>AP511 Invoice Synchronization<br>
                <small>Re-run AP511 invoice synchronization process</small>
            </h1>
        </div>
    </div>
    
    <!-- Modern Navigation Tabs -->
    <div class="modern-tabs-container">
        <div class="row">
            <div class="col-lg-12">
                <div class="modern-tabs">
                    <a href="{{ url('/ap511-check-invoice/dashboard') }}" class="modern-tab">
                        <div class="tab-icon">
                            <i class="fa fa-dashboard"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Dashboard</h4>
                            <span>Overview & Analytics</span>
                        </div>
                    </a>
                    <a href="{{ url('/ap511-check-invoice/list') }}" class="modern-tab">
                        <div class="tab-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Invoice List</h4>
                            <span>Browse Records</span>
                        </div>
                    </a>
                    <a href="{{ url('/ap511-check-invoice/sync') }}" class="modern-tab active">
                        <div class="tab-icon">
                            <i class="fa fa-refresh"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Sync Process</h4>
                            <span>Run Synchronization</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Configuration Section -->
    <div class="modern-filter-section">
        <div class="filter-card">
            <div class="filter-header">
                <h3><i class="fa fa-cog"></i> Synchronization Configuration</h3>
                <div class="sync-status">
                    <span id="sync-status-badge" class="status-badge info">Ready</span>
                </div>
            </div>
            <form id="sync-form" class="modern-form">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="dateFilterType">Date Filter Type <span class="required">*</span></label>
                        <div class="select-wrapper">
                            <select id="dateFilterType" name="dateFilterType" class="modern-select" required>
                                <option value="year">Year Only</option>
                                <option value="dateRange">Date Range</option>
                            </select>
                            <i class="fa fa-chevron-down select-arrow"></i>
                        </div>
                    </div>
                    <div class="filter-group" id="yearFilterGroup">
                        <label for="syncYear">Year <span class="required">*</span></label>
                        <div class="select-wrapper">
                            <select id="syncYear" name="syncYear" class="modern-select" required>
                                @for($year = 2018; $year <= date('Y'); $year++)
                                    <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>{{ $year }}</option>
                                @endfor
                            </select>
                            <i class="fa fa-chevron-down select-arrow"></i>
                        </div>
                    </div>
                    <div class="filter-group" id="dateRangeGroup" style="display: none;">
                        <label for="startDate">Start Date <span class="required">*</span></label>
                        <input type="date" id="startDate" name="startDate" class="modern-input" value="{{ date('Y-01-01') }}">
                    </div>
                    <div class="filter-group" id="endDateGroup" style="display: none;">
                        <label for="endDate">End Date <span class="required">*</span></label>
                        <input type="date" id="endDate" name="endDate" class="modern-input" value="{{ date('Y-12-31') }}">
                    </div>
                    <div class="filter-action">
                        <button type="button" id="startSync" class="modern-btn success">
                            <i class="fa fa-play"></i>
                            <span>Start Synchronization</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Sync Progress Section -->
    <div class="sync-progress-section" id="syncProgressSection" style="display: none;">
        <div class="progress-card">
            <div class="progress-header">
                <h3><i class="fa fa-spinner fa-spin"></i> Synchronization In Progress</h3>
                <div class="progress-actions">
                    <button type="button" id="stopSync" class="modern-btn danger">
                        <i class="fa fa-stop"></i>
                        <span>Stop Process</span>
                    </button>
                </div>
            </div>
            <div class="progress-content">
                <div class="progress-bar-container">
                    <div class="progress-bar">
                        <div class="progress-bar-fill" id="progressBarFill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progressText">0%</span>
                    </div>
                </div>
                <div class="progress-details">
                    <div class="detail-item">
                        <span class="detail-label">Current Date:</span>
                        <span id="currentDate">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Processed Records:</span>
                        <span id="processedRecords">0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Estimated Time Remaining:</span>
                        <span id="estimatedTime">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sync Results Section -->
    <div class="sync-results-section" id="syncResultsSection" style="display: none;">
        <div class="results-card">
            <div class="results-header">
                <h3><i class="fa fa-check-circle"></i> Synchronization Results</h3>
                <div class="results-actions">
                    <button type="button" id="downloadLog" class="modern-btn primary">
                        <i class="fa fa-download"></i>
                        <span>Download Log</span>
                    </button>
                </div>
            </div>
            <div class="results-content">
                <div class="results-summary">
                    <div class="summary-item">
                        <div class="summary-icon success">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="summary-info">
                            <div class="summary-number" id="totalProcessed">0</div>
                            <div class="summary-label">Total Processed</div>
                        </div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-icon warning">
                            <i class="fa fa-exclamation"></i>
                        </div>
                        <div class="summary-info">
                            <div class="summary-number" id="totalErrors">0</div>
                            <div class="summary-label">Errors</div>
                        </div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-icon info">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="summary-info">
                            <div class="summary-number" id="totalTime">0s</div>
                            <div class="summary-label">Total Time</div>
                        </div>
                    </div>
                </div>
                <div class="results-log">
                    <div class="log-header">
                        <h4>Process Log</h4>
                    </div>
                    <div class="log-content" id="logContent">
                        <!-- Log entries will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Modal -->
    <div class="modal-overlay" id="warningModal" style="display: none;">
        <div class="modal-content warning">
            <div class="modal-header">
                <h3><i class="fa fa-exclamation-triangle"></i> Warning</h3>
            </div>
            <div class="modal-body">
                <p>This process will:</p>
                <ul>
                    <li>Delete existing records from the selected date range</li>
                    <li>Re-process all invoice data for the specified period</li>
                    <li>Take a significant amount of time to complete</li>
                </ul>
                <p><strong>Are you sure you want to proceed?</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" id="cancelSync" class="modern-btn secondary">
                    <i class="fa fa-times"></i>
                    <span>Cancel</span>
                </button>
                <button type="button" id="confirmSync" class="modern-btn danger">
                    <i class="fa fa-check"></i>
                    <span>Proceed</span>
                </button>
            </div>
        </div>
    </div>

@endif
@endsection

@section('jsprivate')
<style>
:root {
    --primary-color: #4f46e5;
    --primary-light: #818cf8;
    --primary-dark: #3730a3;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --green-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --blue-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --orange-gradient: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%);
    --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --background-overlay: rgba(255, 255, 255, 0.95);
}

/* Reset and remove old styles */
.nav-pills,
.block,
.block-title,
.block-content {
    all: unset;
}

/* Modern Navigation Tabs */
.modern-tabs-container {
    margin: 30px 0;
    padding: 0 20px;
}

.modern-tabs {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.modern-tab {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    text-decoration: none;
    color: var(--secondary-color);
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.modern-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: var(--transition);
}

.modern-tab.active::before,
.modern-tab:hover::before {
    transform: scaleX(1);
}

.modern-tab:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
    color: var(--primary-color);
}

.modern-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--card-shadow-hover);
}

.tab-icon {
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.modern-tab.active .tab-icon {
    background: rgba(255, 255, 255, 0.2);
}

.tab-content h4 {
    margin: 0 0 5px 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.tab-content span {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Modern Filter Section */
.modern-filter-section {
    margin: 30px 0;
    padding: 0 20px;
}

.filter-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.sync-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.info {
    background: #dbeafe;
    color: #1e40af;
}

.status-badge.success {
    background: #dcfce7;
    color: #166534;
}

.status-badge.warning {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.danger {
    background: #fecaca;
    color: #991b1b;
}

.filter-row {
    display: flex;
    align-items: end;
    gap: 30px;
    padding: 30px;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 0.875rem;
}

.required {
    color: var(--danger-color);
}

.select-wrapper {
    position: relative;
}

.modern-select,
.modern-input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 1rem;
    transition: var(--transition);
}

.modern-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.modern-input {
    padding-right: 16px;
}

.modern-select:focus,
.modern-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.select-arrow {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    pointer-events: none;
}

.filter-action {
    flex-shrink: 0;
}

.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.modern-btn.primary {
    background: var(--primary-color);
    color: white;
}

.modern-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.modern-btn.success {
    background: var(--success-color);
    color: white;
}

.modern-btn.success:hover {
    background: #047857;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.modern-btn.danger {
    background: var(--danger-color);
    color: white;
}

.modern-btn.danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.modern-btn.secondary {
    background: #6b7280;
    color: white;
}

.modern-btn.secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
}

/* Sync Progress Section */
.sync-progress-section {
    margin: 30px 20px;
}

.progress-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.progress-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 24px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.progress-actions {
    display: flex;
    gap: 10px;
}

.progress-content {
    padding: 30px;
}

.progress-bar-container {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-bar-fill {
    height: 100%;
    background: var(--success-color);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: 600;
    color: var(--secondary-color);
}

.progress-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.detail-label {
    font-weight: 500;
    color: var(--secondary-color);
}

/* Sync Results Section */
.sync-results-section {
    margin: 30px 20px;
}

.results-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.results-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 24px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.results-actions {
    display: flex;
    gap: 10px;
}

.results-content {
    padding: 30px;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.summary-icon.success {
    background: var(--success-color);
}

.summary-icon.warning {
    background: var(--warning-color);
}

.summary-icon.info {
    background: var(--info-color);
}

.summary-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 5px;
}

.summary-label {
    color: var(--secondary-color);
    font-size: 0.875rem;
    font-weight: 500;
}

.results-log {
    background: #f8fafc;
    border-radius: 8px;
    overflow: hidden;
}

.log-header {
    background: #e2e8f0;
    padding: 15px 20px;
    border-bottom: 1px solid #cbd5e1;
}

.log-header h4 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1rem;
    font-weight: 600;
}

.log-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow-hover);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-content.warning .modal-header {
    background: linear-gradient(135deg, #fef3c7 0%, #fcd34d 100%);
}

.modal-content.warning .modal-header h3 {
    color: #92400e;
}

.modal-body {
    padding: 30px;
}

.modal-body p {
    margin-bottom: 15px;
    color: var(--secondary-color);
    line-height: 1.6;
}

.modal-body ul {
    margin: 15px 0;
    padding-left: 20px;
    color: var(--secondary-color);
}

.modal-body li {
    margin-bottom: 8px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-tabs {
        flex-direction: column;
        align-items: center;
    }

    .modern-tab {
        min-width: 100%;
        max-width: 400px;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-action {
        align-self: stretch;
    }

    .modern-btn {
        width: 100%;
        justify-content: center;
    }

    .progress-details {
        grid-template-columns: 1fr;
    }

    .results-summary {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Focus States for Accessibility */
.modern-select:focus,
.modern-input:focus,
.modern-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.modern-tab:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 4px;
}
</style>

<script>
$(document).ready(function() {
    var syncProcess = null;
    var syncInterval = null;
    
    // Handle date filter type change
    $('#dateFilterType').change(function() {
        var filterType = $(this).val();
        
        if (filterType === 'year') {
            $('#yearFilterGroup').show();
            $('#dateRangeGroup, #endDateGroup').hide();
            $('#syncYear').attr('required', true);
            $('#startDate, #endDate').attr('required', false);
        } else {
            $('#yearFilterGroup').hide();
            $('#dateRangeGroup, #endDateGroup').show();
            $('#syncYear').attr('required', false);
            $('#startDate, #endDate').attr('required', true);
        }
    });
    
    // Handle start sync button
    $('#startSync').click(function() {
        // Validate form
        if (!validateSyncForm()) {
            return;
        }
        
        // Show warning modal
        $('#warningModal').show();
    });
    
    // Handle cancel sync
    $('#cancelSync').click(function() {
        $('#warningModal').hide();
    });
    
    // Handle confirm sync
    $('#confirmSync').click(function() {
        $('#warningModal').hide();
        startSyncProcess();
    });
    
    // Handle stop sync
    $('#stopSync').click(function() {
        stopSyncProcess();
    });
    
    // Handle download log
    $('#downloadLog').click(function() {
        downloadSyncLog();
    });
    
    function validateSyncForm() {
        var filterType = $('#dateFilterType').val();
        
        if (filterType === 'year') {
            var year = $('#syncYear').val();
            if (!year) {
                alert('Please select a year.');
                return false;
            }
        } else {
            var startDate = $('#startDate').val();
            var endDate = $('#endDate').val();
            
            if (!startDate || !endDate) {
                alert('Please select both start and end dates.');
                return false;
            }
            
            if (new Date(startDate) > new Date(endDate)) {
                alert('Start date cannot be after end date.');
                return false;
            }
        }
        
        return true;
    }
    
    function startSyncProcess() {
        // Update UI to show sync in progress
        $('#syncProgressSection').show();
        $('#syncResultsSection').hide();
        $('#sync-status-badge').removeClass('info success warning danger').addClass('warning').text('Running');
        $('#startSync').prop('disabled', true);
        
        // Reset progress
        updateProgress(0, 'Initializing synchronization...');
        
        // Collect form data
        var formData = {
            filterType: $('#dateFilterType').val(),
            year: $('#syncYear').val(),
            startDate: $('#startDate').val(),
            endDate: $('#endDate').val()
        };
        
        // Start sync process
        $.ajax({
            url: '{{ url("/ap511-check-invoice/sync/start") }}',
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: formData,
            success: function(response) {
                if (response.success) {
                    syncProcess = response.processId;
                    startProgressPolling();
                } else {
                    showError('Failed to start synchronization: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                showError('Error starting synchronization: ' + error);
            }
        });
    }
    
    function stopSyncProcess() {
        if (syncProcess) {
            $.ajax({
                url: '{{ url("/ap511-check-invoice/sync/stop") }}',
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: { processId: syncProcess },
                success: function(response) {
                    if (response.success) {
                        stopProgressPolling();
                        $('#sync-status-badge').removeClass('warning').addClass('danger').text('Stopped');
                        $('#startSync').prop('disabled', false);
                        $('#syncProgressSection').hide();
                    }
                }
            });
        }
    }
    
    function startProgressPolling() {
        syncInterval = setInterval(function() {
            checkSyncProgress();
        }, 2000); // Check every 2 seconds
    }
    
    function stopProgressPolling() {
        if (syncInterval) {
            clearInterval(syncInterval);
            syncInterval = null;
        }
    }
    
    function checkSyncProgress() {
        if (!syncProcess) return;
        
        $.ajax({
            url: '{{ url("/ap511-check-invoice/sync/progress") }}',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: { processId: syncProcess },
            success: function(response) {
                if (response.success) {
                    var progress = response.progress;
                    
                    updateProgress(
                        progress.percentage,
                        progress.currentStatus,
                        progress.currentDate,
                        progress.processedRecords,
                        progress.estimatedTime
                    );
                    
                    if (progress.completed) {
                        stopProgressPolling();
                        showSyncResults(progress.results);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error checking sync progress:', error);
            }
        });
    }
    
    function updateProgress(percentage, status, currentDate, processedRecords, estimatedTime) {
        $('#progressBarFill').css('width', percentage + '%');
        $('#progressText').text(percentage + '%');
        
        if (currentDate) $('#currentDate').text(currentDate);
        if (processedRecords) $('#processedRecords').text(numberWithCommas(processedRecords));
        if (estimatedTime) $('#estimatedTime').text(estimatedTime);
    }
    
    function showSyncResults(results) {
        $('#syncProgressSection').hide();
        $('#syncResultsSection').show();
        $('#sync-status-badge').removeClass('warning').addClass('success').text('Completed');
        $('#startSync').prop('disabled', false);
        
        // Update results summary
        $('#totalProcessed').text(numberWithCommas(results.totalProcessed));
        $('#totalErrors').text(numberWithCommas(results.totalErrors));
        $('#totalTime').text(results.totalTime);
        
        // Update log content
        $('#logContent').html(results.logContent);
    }
    
    function showError(message) {
        $('#syncProgressSection').hide();
        $('#sync-status-badge').removeClass('warning').addClass('danger').text('Error');
        $('#startSync').prop('disabled', false);
        alert(message);
    }
    
    function downloadSyncLog() {
        if (syncProcess) {
            window.open('{{ url("/ap511-check-invoice/sync/log") }}?processId=' + syncProcess, '_blank');
        }
    }
    
    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // Close modal when clicking outside
    $(document).click(function(e) {
        if ($(e.target).is('.modal-overlay')) {
            $('.modal-overlay').hide();
        }
    });
});
</script>
@endsection