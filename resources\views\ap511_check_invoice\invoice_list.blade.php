@extends('layouts.guest-dash')

@section('cssprivate')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
@if (Auth::user())
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="fa fa-list"></i>AP511 Invoice List<br>
                <small>Browse and filter AP511 invoice records</small>
            </h1>
        </div>
    </div>
    
    <!-- Modern Navigation Tabs -->
    <div class="modern-tabs-container">
        <div class="row">
            <div class="col-lg-12">
                <div class="modern-tabs">
                    <a href="{{ url('/ap511-check-invoice/dashboard') }}" class="modern-tab">
                        <div class="tab-icon">
                            <i class="fa fa-dashboard"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Dashboard</h4>
                            <span>Overview & Analytics</span>
                        </div>
                    </a>
                    <a href="{{ url('/ap511-check-invoice/list') }}" class="modern-tab active">
                        <div class="tab-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Invoice List</h4>
                            <span>Browse Records</span>
                        </div>
                    </a>
                    <a href="{{ url('/ap511-check-invoice/sync') }}" class="modern-tab">
                        <div class="tab-icon">
                            <i class="fa fa-refresh"></i>
                        </div>
                        <div class="tab-content">
                            <h4>Sync Process</h4>
                            <span>Run Synchronization</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- <div class="row">
        <div class="col-lg-12 text-right">
            <div class="timestamp-badge">
                <i class="fa fa-clock-o"></i> {{ Carbon\Carbon::now()->format('d-M-Y H:i:s') }}
            </div>
        </div>
    </div> -->

    <!-- Modern Filter Section -->
    <div class="modern-filter-section">
        <div class="filter-card">
            <div class="filter-header">
                <h3><i class="fa fa-filter"></i> Filter Options</h3>
            </div>
            <form id="form-filter" action="{{ url('/ap511-check-invoice/list') }}" method="get" class="modern-form">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="filterYear">Year</label>
                        <div class="select-wrapper">
                            <select id="filterYear" name="year" class="modern-select">
                                <option value="">All Years</option>
                                @foreach($availableYears as $availableYear)
                                    <option value="{{ $availableYear }}" {{ $availableYear == $year ? 'selected' : '' }}>
                                        {{ $availableYear }}
                                    </option>
                                @endforeach
                            </select>
                            <i class="fa fa-chevron-down select-arrow"></i>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label for="filterMonth">Month</label>
                        <div class="select-wrapper">
                            <select id="filterMonth" name="month" class="modern-select">
                                <option value="">All Months</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ $i == $month ? 'selected' : '' }}>
                                        {{ Carbon\Carbon::create()->month($i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                            <i class="fa fa-chevron-down select-arrow"></i>
                        </div>
                    </div>
                    <div class="filter-action">
                        <button type="button" id="applyFilter" class="modern-btn primary">
                            <i class="fa fa-search"></i>
                            <span>Apply Filter</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Modern Table Section -->
    <div class="modern-table-section">
        <div class="table-card">
            <div class="table-header">
                <h3><i class="fa fa-list"></i> Invoice Records</h3>
                <div class="table-actions">
                    <div class="search-info">
                        <span id="table-info"></span>
                    </div>
                    <button type="button" id="exportExcel" class="modern-btn success">
                        <i class="fa fa-file-excel-o"></i>
                        <span>Export to Excel</span>
                    </button>
                </div>
            </div>
            <div class="table-content">
                <div class="table-responsive">
                    <table id="invoiceTable" class="modern-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>POCO No</th>
                                <th>PA No</th>
                                <th>PA Exist</th>
                                <th>Invoice No</th>
                                <th>FL Order ID</th>
                                <th>FL Req ID</th>
                                <th>Status</th>
                                <th>AP511</th>
                                <th>File Name</th>
                                <th>Payment Ref</th>
                                <th>Invoice Date</th>
                                <th>Payment Date</th>
                                <th>Payment Advice</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@endif
@endsection

@section('jsprivate')
<style>
:root {
    --primary-color: #4f46e5;
    --primary-light: #818cf8;
    --primary-dark: #3730a3;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --green-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --blue-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --orange-gradient: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%);
    --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --background-overlay: rgba(255, 255, 255, 0.95);
}

/* Reset and remove old styles */
.nav-pills,
.block,
.block-title,
.block-content {
    all: unset;
}

/* Modern Navigation Tabs */
.modern-tabs-container {
    margin: 30px 0;
    padding: 0 20px;
}

.modern-tabs {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.modern-tab {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    text-decoration: none;
    color: var(--secondary-color);
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.modern-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: var(--transition);
}

.modern-tab.active::before,
.modern-tab:hover::before {
    transform: scaleX(1);
}

.modern-tab:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
    color: var(--primary-color);
}

.modern-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--card-shadow-hover);
}

.tab-icon {
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.modern-tab.active .tab-icon {
    background: rgba(255, 255, 255, 0.2);
}

.tab-content h4 {
    margin: 0 0 5px 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.tab-content span {
    font-size: 1.125rem;
    opacity: 0.8;
}

/* Timestamp Badge */
.timestamp-badge {
    background: var(--background-overlay);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 1.125rem;
    color: var(--secondary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Filter Section */
.modern-filter-section {
    margin: 30px 0;
    padding: 0 20px;
}

.filter-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
}

.filter-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.filter-row {
    display: flex;
    align-items: end;
    gap: 30px;
    padding: 30px;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 1.125rem;
}

.select-wrapper {
    position: relative;
}

.modern-select {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 1.125rem;
    transition: var(--transition);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.modern-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.select-arrow {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    pointer-events: none;
}

.filter-action {
    flex-shrink: 0;
}

.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.125rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.modern-btn.primary {
    background: var(--primary-color);
    color: white;
}

.modern-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.modern-btn.success {
    background: var(--success-color);
    color: white;
}

.modern-btn.success:hover {
    background: #047857;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

/* Modern Table Section */
.modern-table-section {
    margin: 30px 20px;
}

.table-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 24px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.table-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.search-info {
    font-size: 1.125rem;
    color: var(--secondary-color);
    padding: 8px 12px;
    background: rgba(79, 70, 229, 0.1);
    border-radius: 6px;
}

.table-content {
    padding: 0;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table thead th {
    background: #f8fafc;
    padding: 16px 12px;
    text-align: center;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 2px solid #e2e8f0;
    font-size: 1.125rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.modern-table tbody td {
    padding: 16px 12px;
    border-bottom: 1px solid #f1f5f9;
    color: #374151;
    font-size: 1.125rem;
    text-align: center;
    white-space: nowrap;
}

.modern-table tbody tr:hover {
    background: #f8fafc;
}

.modern-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.modern-table tbody tr:nth-child(even):hover {
    background: #f1f5f9;
}

/* Status badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 1.125rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background: #dcfce7;
    color: #166534;
}

.status-badge.danger {
    background: #fecaca;
    color: #991b1b;
}

.status-badge.warning {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.info {
    background: #dbeafe;
    color: #1e40af;
}

/* DataTables Custom Styling */
.dataTables_wrapper {
    padding: 20px 30px;
}

.dataTables_length select,
.dataTables_filter input {
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 1.125rem;
    transition: var(--transition);
}

.dataTables_length select:focus,
.dataTables_filter input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.dataTables_paginate .paginate_button {
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.dataTables_paginate .paginate_button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.dataTables_paginate .paginate_button.current {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.dataTables_info {
    color: var(--secondary-color);
    font-size: 1.125rem;
}

/* Loading state */
.dataTables_processing {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    color: var(--primary-color);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-tabs {
        flex-direction: column;
        align-items: center;
    }

    .modern-tab {
        min-width: 100%;
        max-width: 400px;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-action {
        align-self: stretch;
    }

    .modern-btn {
        width: 100%;
        justify-content: center;
    }

    .modern-table-section {
        margin: 20px 10px;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .modern-table thead th,
    .modern-table tbody td {
        padding: 12px 8px;
        font-size: 1.125rem;
    }

    .dataTables_wrapper {
        padding: 15px 20px;
    }

    .dataTables_length,
    .dataTables_filter,
    .dataTables_info,
    .dataTables_paginate {
        text-align: center;
        margin: 10px 0;
    }
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Focus States for Accessibility */
.modern-select:focus,
.modern-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.modern-tab:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 4px;
}

/* Table scroll indicator */
.table-responsive {
    position: relative;
}

.table-responsive::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.table-responsive.scrollable::after {
    opacity: 1;
}
</style>

<script>
$(document).ready(function() {
    console.log('Invoice List JavaScript loaded');
    
    var table;
    
    // Initialize DataTable with modern styling
    function initDataTable() {
        if (table) {
            table.destroy();
        }
        
        table = $('#invoiceTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ url("/ap511-check-invoice/data") }}',
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: function(d) {
                    d.year = $('#filterYear').val();
                    d.month = $('#filterMonth').val();
                },
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX Error:', error);
                    console.error('Response:', xhr.responseText);
                },
                dataSrc: function(json) {
                    if (json.debug) {
                        console.log('Server debug info:', json.debug);
                    }
                    // Update table info
                    updateTableInfo(json.recordsTotal, json.recordsFiltered);
                    return json.data;
                }
            },
            columns: [
                {data: 'id', name: 'id', className: 'text-center'},
                {data: 'poco_no', name: 'poco_no', className: 'text-center'},
                {data: 'pa_no', name: 'pa_no', className: 'text-center'},
                {
                    data: 'is_pa_no_exist_text', 
                    name: 'is_pa_no_exist', 
                    className: 'text-center', 
                    orderable: false,
                    render: function(data, type, row) {
                        if (data === 'Yes') {
                            return '<span class="status-badge success">Yes</span>';
                        } else {
                            return '<span class="status-badge danger">No</span>';
                        }
                    }
                },
                {data: 'inv_no', name: 'inv_no', className: 'text-center'},
                {data: 'fl_order_id', name: 'fl_order_id', className: 'text-center'},
                {data: 'fl_req_id', name: 'fl_req_id', className: 'text-center'},
                {data: 'status_name', name: 'status_name', className: 'text-center'},
                {
                    data: 'is_ap511_text', 
                    name: 'is_ap511', 
                    className: 'text-center', 
                    orderable: false,
                    render: function(data, type, row) {
                        if (data === 'Yes') {
                            return '<span class="status-badge success">AP511</span>';
                        } else {
                            return '<span class="status-badge warning">Non-AP511</span>';
                        }
                    }
                },
                {data: 'file_name', name: 'file_name', className: 'text-center'},
                {data: 'payment_reference_no', name: 'payment_reference_no', className: 'text-center'},
                {data: 'inv_date_created', name: 'inv_date_created', className: 'text-center'},
                {data: 'payment_date', name: 'payment_date', className: 'text-center'},
                {data: 'payment_advice_no', name: 'payment_advice_no', className: 'text-center'}
            ],
            order: [[0, 'desc']],
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'All']],
            dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-sm-6"i><"col-sm-6"p>>',
            language: {
                processing: '<div style="color: var(--primary-color); font-weight: 600;"><i class="fa fa-spinner fa-spin"></i> Loading invoice data...</div>',
                search: "Search:",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "Showing 0 to 0 of 0 entries",
                infoFiltered: "(filtered from _MAX_ total entries)",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                },
                emptyTable: "No invoice data available",
                zeroRecords: "No matching records found"
            },
            responsive: true,
            scrollX: true,
            autoWidth: false,
            drawCallback: function(settings) {
                // Check if table is scrollable
                var table = $('#invoiceTable');
                var wrapper = table.closest('.table-responsive');
                if (table.width() > wrapper.width()) {
                    wrapper.addClass('scrollable');
                } else {
                    wrapper.removeClass('scrollable');
                }
            }
        });
    }
    
    // Update table info display
    function updateTableInfo(total, filtered) {
        var info = '';
        if (total > 0) {
            if (filtered !== total) {
                info = filtered + ' of ' + total + ' records';
            } else {
                info = total + ' total records';
            }
        } else {
            info = 'No records found';
        }
        $('#table-info').text(info);
    }
    
    // Initialize table on page load
    initDataTable();
    
    // Apply filter button click event
    $('#applyFilter').click(function(e) {
        e.preventDefault();
        
        // Add loading state
        $(this).addClass('loading').prop('disabled', true);
        
        if (table) {
            table.ajax.reload(function() {
                $('#applyFilter').removeClass('loading').prop('disabled', false);
            });
        } else {
            console.log('Table not initialized yet');
            $(this).removeClass('loading').prop('disabled', false);
        }
    });
    
    // Auto-apply filter on dropdown change
    $('#filterYear, #filterMonth').change(function() {
        if (table) {
            table.ajax.reload();
        }
    });

    // Enhance table interactions
    $('#invoiceTable tbody').on('click', 'tr', function() {
        $(this).toggleClass('selected');
    });

    // Export to Excel functionality
    $('#exportExcel').click(function(e) {
        e.preventDefault();
        
        var year = $('#filterYear').val() || '';
        var month = $('#filterMonth').val() || '';
        
        // Generate filename
        var currentDate = new Date();
        var yearMonth = year || currentDate.getFullYear().toString();
        if (month) {
            yearMonth += String(month).padStart(2, '0');
        } else {
            yearMonth += String(currentDate.getMonth() + 1).padStart(2, '0');
        }
        
        // Add loading state
        $(this).addClass('loading').prop('disabled', true);
        var originalText = $(this).find('span').text();
        $(this).find('span').text('Exporting...');
        
        // Show loading overlay
        $('body').append('<div id="exportLoading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.3); z-index: 9999; display: flex; align-items: center; justify-content: center;"><div style="background: white; padding: 30px; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.15); text-align: center;"><i class="fa fa-file-excel-o fa-2x" style="color: var(--success-color); margin-bottom: 16px;"></i><div style="color: #374151; font-weight: 600; margin-bottom: 8px;">Generating Excel Report</div><div style="color: #6b7280; font-size: 1.125rem;">Please wait while we prepare your data...</div></div></div>');
        
        // Create form for export
        var form = $('<form>', {
            'method': 'POST',
            'action': '{{ url("/ap511-check-invoice/export") }}',
            'target': '_blank'
        });
        
        // Add CSRF token
        form.append($('<input>', {
            'type': 'hidden',
            'name': '_token',
            'value': $('meta[name="csrf-token"]').attr('content')
        }));
        
        // Add filter parameters
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'year',
            'value': year
        }));
        
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'month',
            'value': month
        }));
        
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'filename',
            'value': 'InvoicePendingPaymentNotInAP511_' + yearMonth + '.xlsx'
        }));
        
        // Submit form
        form.appendTo('body').submit().remove();
        
        // Reset button state after short delay
        setTimeout(function() {
            $('#exportLoading').remove();
            $('#exportExcel').removeClass('loading').prop('disabled', false);
            $('#exportExcel').find('span').text(originalText);
        }, 3000);
    });

    // Add smooth scroll for better UX
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if(target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});
</script>
@endsection