#!/usr/bin/env python3
"""
Excel Generator Script for AP511 Invoice Export
Generates Excel files with exact formatting to match sample file
"""

import sys
import json
from pathlib import Path
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

def create_excel_file(data, filename):
    """
    Create Excel file with exact formatting from sample
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "2025"
    
    # Define headers exactly as in sample
    headers = [
        'Invoice No.',
        'POCO No.',
        'Payment Advice No.',
        'RE SAP No',
        'FI SAP No',
        'Doc Status',
        'Payment Reference No.',
        'Payment Date',
        'SST No.',
        'Transaction',
        'Type Indicator',
        'Total Payment Amount',
        'Cancellation Date',
        'Remark'
    ]
    
    # Add headers to row 1
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # Define colors and styles
    green_fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
    yellow_fill = PatternFill(start_color="FFC000", end_color="FFC000", fill_type="solid")
    blue_fill = PatternFill(start_color="5B9BD5", end_color="5B9BD5", fill_type="solid")
    
    white_font = Font(color="FFFFFF", bold=True, name="Calibri", size=11)
    data_font = Font(name="Calibri", size=11)
    
    alignment = Alignment(horizontal="left", vertical="center")
    
    # Style header row
    # Columns A-C: Green background
    for col in range(1, 4):
        cell = ws.cell(row=1, column=col)
        cell.fill = green_fill
        cell.font = white_font
        cell.alignment = alignment
    
    # Columns D-G: Yellow background
    for col in range(4, 8):
        cell = ws.cell(row=1, column=col)
        cell.fill = yellow_fill
        cell.font = white_font
        cell.alignment = alignment
    
    # Columns H-N: Blue background
    for col in range(8, 15):
        cell = ws.cell(row=1, column=col)
        cell.fill = blue_fill
        cell.font = white_font
        cell.alignment = alignment
    
    # Set column widths (145px ≈ 20.7 units)
    for col in range(1, 15):
        ws.column_dimensions[get_column_letter(col)].width = 20.7
    
    # Set row height for header
    ws.row_dimensions[1].height = 20
    
    # Add data rows
    for row_num, row_data in enumerate(data, 2):
        # Only populate first 3 columns as per sample
        data_row = [
            row_data.get('inv_no', ''),      # Column A: Invoice No.
            row_data.get('poco_no', ''),     # Column B: POCO No.
            row_data.get('pa_no', ''),       # Column C: Payment Advice No.
            '',                              # Column D: RE SAP No (empty)
            '',                              # Column E: FI SAP No (empty)
            '',                              # Column F: Doc Status (empty)
            '',                              # Column G: Payment Reference No. (empty)
            '',                              # Column H: Payment Date (empty)
            '',                              # Column I: SST No. (empty)
            '',                              # Column J: Transaction (empty)
            '',                              # Column K: Type Indicator (empty)
            '',                              # Column L: Total Payment Amount (empty)
            '',                              # Column M: Cancellation Date (empty)
            ''                               # Column N: Remark (empty)
        ]
        
        # Add data to row
        for col, value in enumerate(data_row, 1):
            cell = ws.cell(row=row_num, column=col, value=value)
            cell.font = data_font
            cell.alignment = alignment
        
        # Set row height
        # ws.row_dimensions[row_num].height = 20
    
    # Remove gridlines for cleaner look
    ws.sheet_view.showGridLines = True
    
    # Save the file
    wb.save(filename)
    return True

def main():
    """
    Main function to process command line arguments and generate Excel file
    """
    if len(sys.argv) != 3:
        print("Usage: python generate_excel.py <json_data_file> <output_filename>")
        sys.exit(1)
    
    json_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        # Read JSON data
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Generate Excel file
        success = create_excel_file(data, output_file)
        
        if success:
            print(f"Excel file generated successfully: {output_file}")
        else:
            print("Failed to generate Excel file")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()